package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// 创建根命令
var rootCmd = &cobra.Command{
	Use:   "skynet-runtime",
	Short: "Skynet Runtime",
	Long:  `Skynet Runtime`,
}

type ServerConfig struct {
	ConfigPath string `json:"config_path"`
	EnableEnv  bool   `json:"enable_env"`
}

var GServerConfig *ServerConfig = &ServerConfig{}

var serverCmd *cobra.Command = &cobra.Command{
	Use:   "server",
	Short: "skynet runtime mesh server",
}

func Init() {
	// 注册 -name 标志
	serverCmd.Flags().StringVarP(&GServerConfig.ConfigPath, "config", "c", "", "config path:file or dir")

	// 注册 -e 开关
	serverCmd.Flags().BoolVarP(&GServerConfig.EnableEnv, "env", "e", false, "是否读取env配置")

	// 将 helloCmd 添加到根命令
	rootCmd.AddCommand(serverCmd)

	// 执行根命令
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
