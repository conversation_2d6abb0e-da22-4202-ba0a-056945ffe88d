package pub

type kafkaConf struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Topic        string `json:"topic" toml:"topic"`
	Partitioner  string `json:"partitioner" toml:"partitioner" default:"rr"`
	RequestMaxMb int    `json:"request_maxmb" toml:"request_maxmb" default:"10"`
	Gzip         bool   `json:"gzip" toml:"gzip" default:"false"`
}

type pubConf struct {
	Type  string    `json:"type" toml:"type" default:"kafka"`
	Kafka kafkaConf `json:"kafka" toml:"kafka"`
}

type confWrapper struct {
	Metrics bool               `json:"metrics" toml:"metrics" deafult:"true"`
	Values  map[string]pubConf `json:"values" toml:"values"`
}

type pubConfWrapper struct {
	Pub confWrapper `json:"pub" toml:"pub"`
}
