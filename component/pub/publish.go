package pub

import (
	"errors"
	"fmt"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"github.com/Shopify/sarama"
	"github.com/bytedance/sonic"
)

type pubInput struct {
	PubName string            `json:"pub_name"`
	Meta    map[string]string `json:"meta"`
	Header  map[string]string `json:"header"`
	Body    string            `json:"body"`
}

type pubOutput struct {
	Partition int32 `json:"partition"`
	Offset    int64 `json:"offset"`
}

func (pc *PubComponent) publishSync(c chains_engine.ChainsContext) {
	rs := rpc_server2.GetRpcServerContextFromContext(c)
	input := &pubInput{
		Meta:   map[string]string{},
		Header: map[string]string{},
		Body:   "",
	}

	err := sonic.Unmarshal(rs.Input, input)
	if err != nil {
		c.Abort(fmt.Errorf("pubInput unmarshal error %v", err))
		return
	}

	pubName := input.PubName

	//pub_metrics.StatsTotal(pubName, 1)

	defer func() {
		if c.Error() != nil {
			//pub_metrics.StatsError(pubName, 1)
		}
	}()

	producer, ok := pc.producers[pubName]
	if !ok {
		c.Abort(errors.New("not find pub conf " + pubName))
		return
	}

	saramaHeader := make([]sarama.RecordHeader, 0, len(input.Header))

	for k, v := range input.Header {
		saramaHeader = append(saramaHeader, sarama.RecordHeader{
			Key:   []byte(k),
			Value: []byte(v),
		})
	}

	partion, offset, err := producer.SyncMessage([]byte(input.Body), saramaHeader, input.Meta)

	if err != nil {
		c.Abort(fmt.Errorf("publish sync error %s %v", pubName, err))
		return
	}
	output := &pubOutput{
		Partition: partion,
		Offset:    offset,
	}
	rs.Output, _ = sonic.Marshal(output)
	return
}
