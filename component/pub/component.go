package pub

import (
	"context"
	"encoding/json"
	"errors"

	"skynet-runtime/component/rpc_server/server_center"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/ppb"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/kafka"
	"github.com/gin-gonic/gin"
)

type PubComponent struct {
	conf      *confWrapper
	producers map[string]*kafka.ProducerHandler
	rs_pub    *rpc_server2.RpcServer
	types.Component
}

var g_pub *PubComponent = &PubComponent{}

func Component() types.Component {
	return g_pub
}

func (pc *PubComponent) Init() error {
	pc.Component = types.NewDefaultComponent(types.ComponentPub)

	temp := &pubConfWrapper{
		Pub: confWrapper{
			Metrics: true,
			Values:  map[string]pubConf{},
		},
	}
	err := goboot.UnmarshalWithConfigAndEnv(types.ComponentPub.EnvName(), temp)
	if err != nil {
		return err
	}

	s, _ := json.Marshal(temp)
	console.Console(types.ComponentPub.ToString(), "config", string(s))

	pc.conf = &temp.Pub

	//pub_metrics.Init(pc.conf.Metrics)

	pc.rs_pub = rpc_server2.NewRpcServer()
	pc.rs_pub.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentProxy))
	pc.rs_pub.AddFunc("io_read", plugins.IoReader)
	pc.rs_pub.AddFunc("pub", pc.publishSync)

	console.Console("pub", "metrics", pc.conf.Metrics)

	if len(pc.conf.Values) == 0 {
		console.Console("pub", "no pub config")
		return nil
	}

	for name, conf := range pc.conf.Values {
		console.Console("pub", name, conf.Type, conf.Kafka)
	}

	return nil
}

func (pc *PubComponent) Run() error {
	pc.producers = map[string]*kafka.ProducerHandler{}

	for name, v := range pc.conf.Values {
		if v.Type == "kafka" {
			opt := &kafka.KafkaProducerModel{}
			opt.UEnable = true
			opt.UName = name
			opt.Brokers = v.Kafka.Brokers
			opt.Topic = v.Kafka.Topic
			opt.Partitioner = v.Kafka.Partitioner
			opt.RequestMaxMb = v.Kafka.RequestMaxMb
			p, err := kafka.NewProducerInstance(opt)
			if err != nil {
				return err
			}
			pc.producers[name] = p
			console.Console("pub", name, "kafka pub init success")
		} else {
			return errors.New("not support pub type")
		}
	}

	//路由注册
	pc.registerServer()
	return nil
}

func (pc *PubComponent) registerServer() {
	console.Console("pub", "http router", "/api/v1/pub")
	r := server_center.GetRpcServer(types.ComponentController)

	// 监听所有 HTTP 方法的所有路径
	r.HttpServer().Router.POST("/api/v1/pub", func(c *gin.Context) {
		rctx := rpc_server2.NewRpcServerContext()
		_, err := pc.rs_pub.GinStart(c, rctx)
		if err != nil {
			c.String(500, err.Error())
		} else {
			c.Data(200, "application/json", rctx.Output)
		}
	})

	console.Console("pub", "grpc router", "/api/v1/pub")
	server_center.RegisterControllerServiceCall(r, "/api/v1/pub", func(ctx context.Context, pb_req *ppb.RpcRequest) (*ppb.RpcResponse, error) {
		rctx := rpc_server2.NewRpcServerContext()
		rctx.Input = pb_req.Data
		_, err := pc.rs_pub.GrpcStart(ctx, rctx)

		return &ppb.RpcResponse{
			Data: rctx.Output,
		}, err
	})
}
