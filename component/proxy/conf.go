package proxy

import "skynet-runtime/internal/health_check"

type ProxyConf struct {
	TargetType string                       `toml:"type" default:"tcp"`
	Mode       string                       `toml:"mode" default:"assist"` //分为assist 和  hijack两种模式，hijack 模式下，会劫持流量
	TargetPort int                          `toml:"target_port"`
	Health     health_check.HealthCheckConf `toml:"health"`
}

type proxyConfWrapper struct {
	P ProxyConf `toml:"proxy"`
}
