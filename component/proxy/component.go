package proxy

import (
	"encoding/json"
	"errors"
	"fmt"
	"skynet-runtime/component/discovery"
	"skynet-runtime/component/rpc_server/hijack/grpc_hijack"
	"skynet-runtime/component/rpc_server/hijack/http_hijack"
	"skynet-runtime/component/rpc_server/hijack/tcp_hijack"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/health_check"
	"skynet-runtime/internal/psingal"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

type ProxyComponent struct {
	types.Component
	enable      bool
	serviceName string
	port        int
	px          *proxy
	watchNodes  *health_check.WatchNode
	hhj         *http_hijack.HttpHiJack
	ghj         *grpc_hijack.GrpcHijack
	thj         *tcp_hijack.TcpHijack
}

var g_proxy *ProxyComponent = &ProxyComponent{}

func Component() types.Component {
	return g_proxy
}

func (pc *ProxyComponent) Init() error {
	pc.Component = types.NewDefaultComponent(types.ComponentProxy)

	pc.serviceName = goboot.BootConf().DefaultConfig().ServiceName
	pc.port = goboot.BootConf().DefaultConfig().Port

	if pc.port == 0 {
		console.Console("proxy", "not config port")
		return nil
	}

	if pc.serviceName == "" {
		return errors.New("proxy service_name is zero")
	}

	gw := &proxyConfWrapper{}

	err := goboot.UnmarshalWithConfigAndEnv(types.ComponentProxy.EnvName(), gw)
	if err != nil {
		return err
	}

	s, _ := json.Marshal(gw)
	console.Console(types.ComponentProxy.ToString(), "config", string(s))

	health_check.SetLogger(pc.Log())

	if gw.P.Mode == "assist" {
		return pc.initAssist(gw)
	} else if gw.P.Mode == "hijack" {
		return pc.initHiJack(gw)
	}

	return errors.New("proxy mode is error:" + gw.P.Mode)
}

func (pc *ProxyComponent) initHiJack(gw *proxyConfWrapper) error {

	if gw.P.TargetPort == 0 {
		return errors.New("proxy target_port is zero")
	}

	targetAddr := types.NewServerPort(gw.P.TargetPort)
	listenAddr := types.NewServerPort(pc.port)
	console.Console("proxy hiJack target", targetAddr.AddrInfo())
	pc.px = newTcpProxy(false, []string{targetAddr.AddrInfo()})

	var err error
	if gw.P.TargetType == types.ServerHttp {
		pc.hhj = http_hijack.NewHttpHiJack(listenAddr, targetAddr)
	} else if gw.P.TargetType == types.ServerGrpc {
		pc.ghj, err = grpc_hijack.NewGrpcHijack(listenAddr, targetAddr)
		if err != nil {
			return err
		}
	} else if gw.P.TargetType == types.ServerTcp {
		pc.thj = tcp_hijack.NewTcpHijack(listenAddr, targetAddr)
	} else {
		return fmt.Errorf("unsupport target type %s", gw.P.TargetType)
	}

	hw, err := health_check.NewWatchNode(targetAddr.AddrInfo(), types.ServerTcp, gw.P.Health)
	if err != nil {
		return err
	}

	ok := hw.Readness()

	if !ok {
		return fmt.Errorf("%s read ness fail", hw.Addr)
	}

	console.Console("proxy", "readness success", targetAddr.AddrInfo())
	err = discovery.Register()

	if err != nil {
		return err
	}

	pc.watchNodes = hw
	pc.enable = true
	go pc.wait()
	return nil
}

func (pc *ProxyComponent) initAssist(gw *proxyConfWrapper) error {
	addr := fmt.Sprintf("0.0.0.0:%d", pc.port)

	console.Console("proxy assist target", addr)
	pc.px = newTcpProxy(false, []string{addr})

	hw, err := health_check.NewWatchNode(addr, types.ServerTcp, gw.P.Health)
	if err != nil {
		return err
	}

	ok := hw.Readness()

	if !ok {
		return fmt.Errorf("%s read ness fail", hw.Addr)
	}

	console.Console("proxy", "readness success", addr)
	err = discovery.Register()

	if err != nil {
		return err
	}

	pc.watchNodes = hw
	pc.enable = true
	go pc.wait()
	return nil
}

func (pc *ProxyComponent) Run() error {
	if pc.hhj != nil {
		pc.hhj.Start()
	}

	if pc.ghj != nil {
		pc.ghj.Start()
	}

	if pc.thj != nil {
		pc.thj.Start()
	}

	return nil
}

func (pc *ProxyComponent) Exit() {
	if pc.enable {
		discovery.UnRegister()
	}
}

func (pc *ProxyComponent) wait() {
	//异步探活

	fail_chan := make(chan string)
	go pc.watchNodes.Watch(fail_chan)

	fails := <-fail_chan

	console.Console("proxy", "proxy target liveness fail", fails)

	//发出退出信号
	psingal.Exit()
	return
}
