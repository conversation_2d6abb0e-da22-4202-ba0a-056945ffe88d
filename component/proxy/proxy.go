package proxy

import (
	"net"
	"net/http"
	"skynet-runtime/internal/types"
	"sync"
)

type proxy struct {
	targets      []string
	currentIndex int
	mu           sync.Mutex
	proxyType    string
	unix         bool
	mHttpClient  map[string]*http.Client
}

// 获取下一个目标地址
func (p *proxy) Next() string {
	p.mu.Lock()
	defer p.mu.Unlock()

	if len(p.targets) == 0 {
		return ""
	}

	target := p.targets[p.currentIndex]
	// 循环轮询
	p.currentIndex = (p.currentIndex + 1) % len(p.targets)

	return target
}

// IsHttp 判断是否为HTTP代理
func (p *proxy) IsHttp() bool {
	return p.proxyType == types.ServerHttp
}

// IsGrpc 判断是否为GRPC代理
func (p *proxy) IsGrpc() bool {
	return p.proxyType == types.ServerGrpc
}

func (p *proxy) IsTcp() bool {
	return p.proxyType == types.ServerTcp
}

func (p *proxy) ProxyType() string {
	return p.proxyType
}

func (p *proxy) HttpClient(target string) *http.Client {
	return p.mHttpClient[target]
}

// NewProxy 创建新的代理实例
func newHttpProxy(unix bool, targets []string) *proxy {
	p := &proxy{
		targets:      targets,
		currentIndex: 0,
		proxyType:    types.ServerHttp,
		unix:         unix,
		mHttpClient:  make(map[string]*http.Client),
	}

	for _, target := range targets {
		if unix {
			socketPath := target
			// 创建自定义 Transport，使用 Unix 域套接字
			tr := &http.Transport{
				Dial: func(network, addr string) (net.Conn, error) {
					return net.Dial("unix", socketPath)
				},
			}

			// 创建 HTTP 客户端，使用自定义 Transport
			p.mHttpClient[target] = &http.Client{
				Transport: tr,
			}
		} else {
			p.mHttpClient[target] = http.DefaultClient
		}
	}

	return p
}

func newGrpcProxy(unix bool, targets []string) *proxy {
	return &proxy{
		targets:      targets,
		currentIndex: 0,
		proxyType:    types.ServerGrpc,
		unix:         unix,
		mHttpClient:  make(map[string]*http.Client),
	}
}

func newTcpProxy(unix bool, targets []string) *proxy {
	return &proxy{
		targets:      targets,
		currentIndex: 0,
		proxyType:    types.ServerTcp,
		unix:         unix,
		mHttpClient:  make(map[string]*http.Client),
	}
}
