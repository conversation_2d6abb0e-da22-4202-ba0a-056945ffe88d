package rpc_server_wraper

import (
	"os"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"

	gservice "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/grpc_server/services"
	hservice "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server/services"
)

const prefix_unix_sock = "/skynet-runtime/unix/"

func init() {
	os.Remove(prefix_unix_sock)
	console.Console("sever wrapper", "sock pre path", prefix_unix_sock)
}

type severWraper struct {
	name      types.ComponentType
	http_addr types.Port
	grpc_addr types.Port

	//bunix     bool
	//typeSever string
	http_instance *hservice.HttpServer
	grpc_instance *gservice.GrpcServer
}

type RpcServer interface {
	ComponentName() types.ComponentType
	Addr() (string, string)
	HttpServer() *hservice.HttpServer
	GrpcServer() *gservice.GrpcServer
	NewHttpServerWithAddr(s types.Port)
	NewGrpcWithAddr(s types.Port)
}

func NewRpcServer(name types.ComponentType) RpcServer {
	sw := &severWraper{
		name: name,
	}

	return sw
}

func (s *severWraper) ComponentName() types.ComponentType {
	return s.name
}

func (s *severWraper) Addr() (string, string) {
	return s.http_addr.AddrInfo(), s.grpc_addr.AddrInfo()
}

func (sw *severWraper) HttpServer() *hservice.HttpServer {
	return sw.http_instance
}

func (sw *severWraper) GrpcServer() *gservice.GrpcServer {
	return sw.grpc_instance
}

func (sw *severWraper) NewGrpcWithAddr(s types.Port) {
	sw.grpc_addr = s
	sw.newGrpcInstance()
	return
}

func (sw *severWraper) NewHttpServerWithAddr(s types.Port) {
	sw.http_addr = s
	sw.newHttpInstance()
	return
}
