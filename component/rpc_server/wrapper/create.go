package rpc_server_wraper

import (
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
	gmodel "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/grpc_server/model"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/grpc_server/services"
	gservice "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/grpc_server/services"
	hmodel "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server/model"
	hservice "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server/services"
	metrics_middle "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/server_middle/metrics"
	"github.com/gin-gonic/gin"
)

func (sw *severWraper) newHttpInstance() {
	if sw.http_addr.AddrInfo() == "" {
		return
	}

	option := &hmodel.HttpServerModel{
		BaseModel: base_model.BaseModel{
			UEnable: true,
			UName:   sw.name.ToString(),
		},
		Metrics: metrics_middle.MetricsMiddleModel{
			Disabled: true,
		},
	}

	option.Port = sw.http_addr.ToInt()

	gin.SetMode(gin.ReleaseMode)

	sh := &hservice.HttpServer{
		Model:  option,
		Router: gin.New(),
	}

	//sh.Router.Use(hservice.BaseGinMiddle(option.Name()))
	sw.http_instance = sh
	return
}

func (sw *severWraper) newGrpcInstance() {
	if sw.grpc_addr.AddrInfo() == "" {
		return
	}

	option := &gmodel.GrpcServerModel{
		BaseModel: base_model.BaseModel{
			UEnable: true,
			UName:   sw.name.ToString(),
		},
		Metrics: metrics_middle.MetricsMiddleModel{
			Disabled: true,
		},
	}

	option.Port = sw.grpc_addr.ToInt()

	gs := gservice.NewGrpcServer(option)
	gs.UnaryServerInterceptor = append(gs.UnaryServerInterceptor, services.BaseGrpcMiddle(option.Name()))

	sw.grpc_instance = gs
	return
}
