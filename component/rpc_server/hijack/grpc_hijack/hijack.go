package grpc_hijack

import (
	"context"
	"errors"
	"fmt"
	"io"
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/grpc_pools"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/types"
	"sync"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/grpc_conns"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type GrpcHijack struct {
	types.Component
	listen types.Port
	target types.Port
	rpc_server_wraper.RpcServer
	rs        *rpc_server2.RpcServer
	grpc_pool *grpc_conns.GrpcConnsPool
}

func NewGrpcHijack(listen types.Port, target types.Port) (*GrpcHijack, error) {
	console.Console("grpc hijack", listen.AddrInfo(), target.AddrInfo())
	ghj := &GrpcHijack{
		listen:    listen,
		target:    target,
		Component: types.NewDefaultComponent(types.ComponentGrpcHijack),
	}

	ghj.rs = rpc_server2.NewRpcServer()

	pool, err := grpc_conns.NewGrpcConnPool(grpc_conns.WithFactory(&grpc_pools.Factory{}))
	if err != nil {
		return nil, fmt.Errorf("new grpc forward conns pool error:%v", err)
	}
	ghj.grpc_pool = pool
	ghj.rs.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentGrpcHijack))
	//ghj.rs.AddFunc("resolver", plugins.ResolverTlb)
	ghj.rs.AddFunc("metrics", plugins.HijackMetrics())
	ghj.rs.AddFunc("hijack", ghj.hijack)
	ghj.RpcServer = rpc_server_wraper.NewRpcServer(types.ComponentGrpcHijack)
	ghj.RpcServer.NewGrpcWithAddr(ghj.listen)
	return ghj, nil
}

func (ghj *GrpcHijack) Start() {
	ghj.GrpcServer().ServerOptions = append(ghj.GrpcServer().ServerOptions, grpc.UnknownServiceHandler(ghj.proxyHandler))
	ghj.GrpcServer().ServerOptions = append(ghj.GrpcServer().ServerOptions, grpc.CustomCodec(&grpc_pools.ProtoCode{}))
	ghj.GrpcServer().RunGrpcServer()
}

type srvKey struct{}
type streamKey struct{}

func (gf *GrpcHijack) proxyHandler(srv any, stream grpc.ServerStream) error {
	rpcx := rpc_server2.NewRpcServerContext()
	fullMethodName, _ := grpc.MethodFromServerStream(stream)
	rpcx.Path = fullMethodName
	rpcx.Uri = fullMethodName
	chainsCtx := chains_engine.NewChainsContext()
	chainsCtx.SetStore(srvKey{}, srv)
	chainsCtx.SetStore(streamKey{}, stream)

	err := gf.rs.GrpcStartWithChainContext(stream.Context(), rpcx, chainsCtx)

	if err != nil {
		gf.Component.Log().Error(gf.ComponentName().ToString(), zap.String("error", err.Error()))
	}

	return err
}
func (gf *GrpcHijack) hijack(c chains_engine.ChainsContext) {
	app := plugins.GetSkynetAppFromContxt(c)

	isStream := false
	if app.AppGrpcType == "stream" {
		isStream = true
	} else if app.AppGrpcType == "unary" {
		isStream = false
	} else {
		c.Abort(fmt.Errorf("invalid app.AppGrpcType %s", app.AppGrpcType))
		return
	}

	stream := c.GetStoreWithDef(streamKey{}, nil).(grpc.ServerStream)
	fullMethodName, _ := grpc.MethodFromServerStream(stream)
	addr := gf.target.AddrInfo()
	// 创建客户端流
	var header metadata.MD

	md, _ := metadata.FromIncomingContext(stream.Context())

	ctx := metadata.NewOutgoingContext(stream.Context(), md)
	_, err := gf.grpc_pool.Call(ctx, addr, func(ctx context.Context, addr string, conn *grpc.ClientConn) (any, error) {
		clientStream, err := grpc.NewClientStream(
			ctx,
			&grpc.StreamDesc{ServerStreams: true, ClientStreams: true},
			conn,
			fullMethodName,
		)

		if err != nil {
			return nil, err
		}

		defer func() {
			header, _ = clientStream.Header()
			clientStream.CloseSend()
		}()

		f := newForward(clientStream, stream)

		if isStream {
			err = f.forwardStream()
		} else {
			err = f.forwardUnary()
		}

		return nil, err
	})

	if header.Len() > 0 {
		c.SetStore(plugins.GrpcHeaderKey{}, header)
	}

	if err != nil {
		c.Abort(err)
		return
	}

	return
}

type forward struct {
	clientStream grpc.ClientStream
	serverStream grpc.ServerStream
}

func newForward(clientStream grpc.ClientStream, serverStream grpc.ServerStream) *forward {
	return &forward{
		clientStream: clientStream,
		serverStream: serverStream,
	}
}

func (gf *forward) forwardStream() error {
	ret := make(chan error, 2)
	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer func() {
			wg.Done()
		}()
		//读尽
		for {
			msg := &grpc_pools.Frame{}
			err := gf.serverStream.RecvMsg(msg)
			if err != nil {
				ret <- err
				break
			}

			err = gf.clientStream.SendMsg(msg)
			if err != nil {
				ret <- err
				break
			}
		}
	}()

	go func() {
		defer func() {
			wg.Done()
		}()
		for {
			msg := &grpc_pools.Frame{}
			err := gf.clientStream.RecvMsg(msg)
			if err != nil {
				ret <- err
				break
			}
			err = gf.serverStream.SendMsg(msg)
			if err != nil {
				ret <- err
				break
			}
		}
	}()

	wg.Wait()

	var errReturn error
	for i := 0; i < 2; i++ {
		err := <-ret
		if !errors.Is(err, io.EOF) {
			errReturn = err
		}
	}

	return errReturn
}

func (gf *forward) forwardUnary() error {
	msg_a := &grpc_pools.Frame{}
	err := gf.serverStream.RecvMsg(msg_a)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	err = gf.clientStream.SendMsg(msg_a)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	msg_b := &grpc_pools.Frame{}
	err = gf.clientStream.RecvMsg(msg_b)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	header, _ := gf.clientStream.Header()
	if len(header) > 0 {
		gf.serverStream.SetHeader(header)
	}

	err = gf.serverStream.SendMsg(msg_b)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	return nil
}
