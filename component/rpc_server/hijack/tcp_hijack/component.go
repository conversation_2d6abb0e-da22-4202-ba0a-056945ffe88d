package tcp_hijack

import (
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"
	"time"
)

type TcpHijack struct {
	listen types.Port
	target types.Port
	types.Component
}

func NewTcpHijack(listen types.Port, target types.Port) *TcpHijack {
	console.Console("new tcp hijack ", listen.AddrInfo(), target.AddrInfo())
	return &TcpHijack{
		listen:    listen,
		target:    target,
		Component: types.NewDefaultComponent(types.ComponentTcpHijack),
	}
}

func (pc *TcpHijack) Start() {
	console.Console("tcp hijack run ", pc.listen.AddrInfo(), pc.target.AddrInfo())
	//启动四层代理监听
	go pc.forwardListen(time.Duration(1000) * time.Millisecond)
	return
}
