package tcp_hijack

import (
	"fmt"
	"io"
	"net"
	"skynet-runtime/internal/mmetrics"
	"time"
)

func (tj *TcpHijack) forwardListen(timeout time.Duration) error {
	// 监听在本地 8000 端口（代理端口）
	listenAddr := tj.listen.AddrInfo()

	listener, err := net.Listen("tcp", listenAddr)
	if err != nil {
		return fmt.Errorf("tcp forward listen fail: %v", err)
	}

	for {
		clientConn, err := listener.Accept()
		if err != nil {
			tj.Log().Error(fmt.Sprintf("tcp forward accept fail: %v", err))
			continue
		}

		// 每个连接都开启一个 goroutine 处理转发
		go tj.handleConnection(clientConn, timeout)
	}
}
func (tj *TcpHijack) handleConnection(clientConn net.Conn, timeout time.Duration) {
	st := time.Now()
	proto := "tcp"

	mmetrics.HijackTcpConnections.GuageInc(1, "", "tcp")

	tj.Log().Info(fmt.Sprintf("create tcp forward connection %s-%s-%s", clientConn.RemoteAddr(), clientConn.LocalAddr(), tj.target.AddrInfo()))
	defer func() {
		mmetrics.HijackTcpConnections.GuageDec(1, "", "tcp")
		e := time.Now().Sub(st).Milliseconds()
		clientConn.Close()
		tj.Log().Info(fmt.Sprintf("close tcp forward connection %s-%s-%s,elapse mills:%d", clientConn.RemoteAddr(), clientConn.LocalAddr(), tj.target.AddrInfo(), e))
	}()

	targetConn, err := net.DialTimeout(proto, tj.target.AddrInfo(), timeout)
	if err != nil {
		tj.Log().Error(fmt.Sprintf("tcp forward dial fail: %v", err))
		return
	}
	defer targetConn.Close()

	// 双向复制数据
	go io.Copy(targetConn, clientConn) // client → target
	io.Copy(clientConn, targetConn)    // target → client
}
