package http_hijack

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httputil"
	"net/url"
	"skynet-runtime/component/pandora"
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/skynet_proto"
	"skynet-runtime/internal/types"
	"strings"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HttpHiJack struct {
	listen types.Port
	target types.Port
	types.Component
	rpc_server_wraper.RpcServer
	rs *rpc_server2.RpcServer
}

func NewHttpHiJack(listen types.Port, target types.Port) *HttpHiJack {
	console.Console("http hijack", listen.AddrInfo(), target.AddrInfo())
	hhj := &HttpHiJack{
		listen:    listen,
		target:    target,
		Component: types.NewDefaultComponent(types.ComponentHttpHijack),
	}

	rs := rpc_server2.NewRpcServer()

	rs.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentHttpHijack))
	//rs.AddFunc("resolver", plugins.ResolverTlb)
	rs.AddFunc("metrics", plugins.HijackMetrics())
	rs.AddFunc("hijack", hhj.forward2)
	hhj.rs = rs
	hhj.RpcServer = rpc_server_wraper.NewRpcServer(types.ComponentTcpHijack)
	hhj.RpcServer.NewHttpServerWithAddr(hhj.listen)
	return hhj
}

func (hhj *HttpHiJack) Start() error {
	hhj.HttpServer().Router.Any("/*path", func(c *gin.Context) {
		rctx := rpc_server2.NewRpcServerContext()
		rctx.Path = c.Request.URL.Path
		rctx.Uri = c.Request.RequestURI

		ccx := chains_engine.NewChainsContext()

		err := hhj.rs.GinStartWithChainContext(c, rctx, ccx)
		if err != nil {
			hhj.Component.Log().Error(types.ComponentHttpHijack.ToString(), zap.String("error", err.Error()))
			c.String(500, err.Error())
		}
	})

	hhj.HttpServer().RunHttpServer()
	return nil
}

func (hf *HttpHiJack) forward(c chains_engine.ChainsContext) {

	rs := rpc_server2.GetRpcServerContextFromContext(c)

	var proxy *httputil.ReverseProxy

	target, err := url.Parse(fmt.Sprintf("http://%s/", hf.target.AddrInfo()))
	if err != nil {
		c.Abort(fmt.Errorf("invalid target url %s %v", hf.target.AddrInfo(), err))
		return
	}

	proxy = httputil.NewSingleHostReverseProxy(target)

	// 捕获下游响应
	proxy.ModifyResponse = func(resp *http.Response) error {
		if resp.StatusCode != http.StatusOK {
			c.SetStore(plugins.HttpStatusKey{}, resp.Status)
		} else {
			status := resp.Header.Get(skynet_proto.AppHeaderStatusCode)
			if status != "" && status != "0" && status != "200" {
				c.SetStore(plugins.HttpStatusKey{}, status)
			}
		}

		return nil
	}

	proxy.ServeHTTP(rs.GinContext.Writer, rs.GinContext.Request)
}

func (hf *HttpHiJack) forward2(c chains_engine.ChainsContext) {

	rs := rpc_server2.GetRpcServerContextFromContext(c)

	var proxy *httputil.ReverseProxy

	target, err := url.Parse(fmt.Sprintf("http://%s/", hf.target.AddrInfo()))
	if err != nil {
		c.Abort(fmt.Errorf("invalid target url %s %v", hf.target.AddrInfo(), err))
		return
	}

	proxy = httputil.NewSingleHostReverseProxy(target)

	if strings.HasPrefix(rs.GinContext.Request.URL.Path, "/pandora") && rs.GinContext.Request.Body != nil {
		bodyBytes, err := io.ReadAll(rs.GinContext.Request.Body)
		if err != nil {
			c.Abort(fmt.Errorf("[/pandora] io readall failed:%s %v", rs.GinContext.Request.URL.Path, err))
			return
		}

		rs.GinContext.Request.Body.Close()

		if len(bodyBytes) != 0 {
			var bodyData pandora.PandoraProto
			if err := json.Unmarshal(bodyBytes, &bodyData); err != nil {
				c.Abort(fmt.Errorf("[/pandora] json Unmarshal failed:%s %v", rs.GinContext.Request.URL.Path, err))
				return
			}

			newBodyBytes, err := json.Marshal(bodyData.Payload)

			if err != nil {
				c.Abort(fmt.Errorf("[/pandora] json Marshal failed:%s %v", rs.GinContext.Request.URL.Path, err))
				return
			}

			rs.GinContext.Request.Body = io.NopCloser(bytes.NewReader(newBodyBytes))
			rs.GinContext.Request.ContentLength = int64(len(newBodyBytes))
			rs.GinContext.Request.Header.Set("Content-Type", "application/json")
		}

	}

	// 捕获下游响应
	proxy.ModifyResponse = func(resp *http.Response) error {
		if resp.StatusCode != http.StatusOK {
			c.SetStore(plugins.HttpStatusKey{}, resp.Status)
		} else {
			status := resp.Header.Get(skynet_proto.AppHeaderStatusCode)
			if status != "" && status != "0" && status != "200" {
				c.SetStore(plugins.HttpStatusKey{}, status)
			}
		}

		return nil
	}

	proxy.ServeHTTP(rs.GinContext.Writer, rs.GinContext.Request)
}
