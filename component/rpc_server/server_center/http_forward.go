package server_center

import (
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/skynet_proto"
	"skynet-runtime/internal/types"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type HttpForward struct {
	types.Component
	port types.Port
	rpc_server_wraper.RpcServer
	rs *rpc_server2.RpcServer
}

func (f *ServerCenterComponent) newHttpForwardServer(port int) error {
	console.Console(types.ComponentHttpForward, "new http forward server", port)
	ls := &HttpForward{
		port:      types.NewServerPort(port),
		Component: types.NewDefaultComponent(types.ComponentHttpForward),
	}
	ls.rs = rpc_server2.NewRpcServer()

	ls.rs.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentHttpForward))
	ls.rs.AddFunc("resolver", plugins.ResolverTlb)
	ls.rs.AddFunc("metrics", plugins.ForwardMetrics())
	ls.rs.AddFunc("forward", ls.forward)
	ls.RpcServer = rpc_server_wraper.NewRpcServer(types.ComponentHttpForward)
	ls.RpcServer.NewHttpServerWithAddr(ls.port)
	f.mSevers[types.ComponentHttpForward] = ls
	return nil
}

func (f *ServerCenterComponent) runHttpForward() {
	if f.mSevers[types.ComponentHttpForward] == nil {
		return
	}
	hf := f.mSevers[types.ComponentHttpForward].(*HttpForward)
	hf.HttpServer().Router.Any("/*path", func(c *gin.Context) {
		rctx := rpc_server2.NewRpcServerContext()
		rctx.Path = c.Request.URL.Path
		rctx.Uri = c.Request.RequestURI

		_, err := hf.rs.GinStart(c, rctx)
		if err != nil {
			f.Component.Log().Error(types.ComponentHttpForward.ToString(), zap.String("error", err.Error()))
			c.String(500, err.Error())
		}
	})

	f.mSevers[types.ComponentHttpForward].HttpServer().RunHttpServer()
}

func (hf *HttpForward) forward(c chains_engine.ChainsContext) {
	addr := plugins.GetResolverFromContext(c)

	rs := rpc_server2.GetRpcServerContextFromContext(c)

	// 目标服务地址
	target, err := url.Parse(fmt.Sprintf("http://%s/", addr))
	if err != nil {
		c.Abort(fmt.Errorf("invalid target url %s %v", addr, err))
		return
	}
	proxy := httputil.NewSingleHostReverseProxy(target)

	// 捕获下游响应
	proxy.ModifyResponse = func(resp *http.Response) error {
		if resp.StatusCode != http.StatusOK {
			c.SetStore(plugins.HttpStatusKey{}, resp.Status)
		} else {
			status := resp.Header.Get(skynet_proto.AppHeaderStatusCode)
			if status != "" && status != "0" && status != "200" {
				c.SetStore(plugins.HttpStatusKey{}, status)
			}
		}

		return nil
	}

	proxy.ServeHTTP(rs.GinContext.Writer, rs.GinContext.Request)
}
