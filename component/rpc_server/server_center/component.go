package server_center

import (
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

type ServerCenterComponent struct {
	mSevers map[types.ComponentType]rpc_server_wraper.RpcServer
	types.Component
}

var g_server_center *ServerCenterComponent = &ServerCenterComponent{
	mSevers: make(map[types.ComponentType]rpc_server_wraper.RpcServer),
}
var GModel ServerModel

func Component() types.Component {
	return g_server_center
}

// Init 初始化组件
func (f *ServerCenterComponent) Init() error {
	f.Component = types.NewDefaultComponent(types.ComponentServerCenter)

	f.mSevers = make(map[types.ComponentType]rpc_server_wraper.RpcServer)

	mw := &modelWrapper{}

	err := goboot.UnmarshalWithConfigAndEnv(types.ComponentServerCenter.EnvName(), mw)

	if err != nil {
		return err
	}

	GModel = mw.S

	if !mw.S.Enabled {
		return nil
	}

	if mw.S.GrpcForwardPort != 0 {
		err := f.newGrpcForwardServer(mw.S.GrpcForwardPort)
		if err != nil {
			return err
		}
	}

	if mw.S.HttpForwardPort != 0 {
		err := f.newHttpForwardServer(mw.S.HttpForwardPort)
		if err != nil {
			return err
		}
	}

	if mw.S.ControllerPort != 0 {
		err := f.newControllerServer(mw.S.ControllerPort, mw.S.MetricsUrl)
		if err != nil {
			return err
		}
	}
	return nil
}

// Run 运行组件
func (f *ServerCenterComponent) Run() error {
	/*
		err := f.runListenServer()
		if err != nil {
			return err
		}
	*/

	f.runHttpForward()
	f.runGrpcForward()
	f.runControllerServer()
	return nil
}

// Exit 退出组件
func (f *ServerCenterComponent) Exit() {
	f.exitControllerServer()
}

func GetRpcServer(name types.ComponentType) rpc_server_wraper.RpcServer {
	return g_server_center.mSevers[name]
}
