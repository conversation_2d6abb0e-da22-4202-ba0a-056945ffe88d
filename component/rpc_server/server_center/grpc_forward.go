package server_center

import (
	"context"
	"errors"
	"fmt"
	"io"
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/grpc_pools"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/types"
	"sync"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/grpc_conns"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type GrpcForward struct {
	types.Component
	port types.Port
	rpc_server_wraper.RpcServer
	rs        *rpc_server2.RpcServer
	grpc_pool *grpc_conns.GrpcConnsPool
}

func (f *ServerCenterComponent) newGrpcForwardServer(port int) error {
	console.Console(types.ComponentGrpcForward, "new grpc forward server", port)
	ls := &GrpcForward{
		Component: types.NewDefaultComponent(types.ComponentGrpcForward),
		port:      types.NewServerPort(port),
	}

	ls.rs = rpc_server2.NewRpcServer()

	pool, err := grpc_conns.NewGrpcConnPool(grpc_conns.WithFactory(&grpc_pools.Factory{}))
	if err != nil {
		return fmt.Errorf("new grpc forward conns pool error:%v", err)
	}
	ls.grpc_pool = pool
	ls.rs.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentGrpcForward))
	ls.rs.AddFunc("resolver", plugins.ResolverTlb)
	ls.rs.AddFunc("metrics", plugins.ForwardMetrics())
	ls.rs.AddFunc("forward", ls.forward)
	ls.RpcServer = rpc_server_wraper.NewRpcServer(types.ComponentGrpcForward)
	ls.RpcServer.NewGrpcWithAddr(ls.port)
	f.mSevers[types.ComponentGrpcForward] = ls
	return nil
}

func (f *ServerCenterComponent) runGrpcForward() {
	if f.mSevers[types.ComponentGrpcForward] == nil {
		return
	}
	hf := f.mSevers[types.ComponentGrpcForward].(*GrpcForward)
	hf.GrpcServer().ServerOptions = append(hf.GrpcServer().ServerOptions, grpc.UnknownServiceHandler(hf.proxyHandler))
	hf.GrpcServer().ServerOptions = append(hf.GrpcServer().ServerOptions, grpc.CustomCodec(&grpc_pools.ProtoCode{}))
	f.mSevers[types.ComponentGrpcForward].GrpcServer().RunGrpcServer()
}

type srvKey struct{}
type streamKey struct{}

func (gf *GrpcForward) proxyHandler(srv any, stream grpc.ServerStream) error {
	rpcx := rpc_server2.NewRpcServerContext()
	fullMethodName, _ := grpc.MethodFromServerStream(stream)
	rpcx.Path = fullMethodName
	rpcx.Uri = fullMethodName
	chainsCtx := chains_engine.NewChainsContext()
	chainsCtx.SetStore(srvKey{}, srv)
	chainsCtx.SetStore(streamKey{}, stream)

	err := gf.rs.GrpcStartWithChainContext(stream.Context(), rpcx, chainsCtx)

	if err != nil {
		gf.Component.Log().Error(gf.ComponentName().ToString(), zap.String("error", err.Error()))
	}

	return err
}
func (gf *GrpcForward) forward(c chains_engine.ChainsContext) {
	app := plugins.GetSkynetAppFromContxt(c)

	isStream := false
	if app.AppGrpcType == "stream" {
		isStream = true
	} else if app.AppGrpcType == "unary" {
		isStream = false
	} else {
		c.Abort(fmt.Errorf("invalid app.AppGrpcType %s", app.AppGrpcType))
		return
	}

	stream := c.GetStoreWithDef(streamKey{}, nil).(grpc.ServerStream)
	fullMethodName, _ := grpc.MethodFromServerStream(stream)
	addr := plugins.GetResolverFromContext(c)
	// 创建客户端流
	var header metadata.MD

	md, _ := metadata.FromIncomingContext(stream.Context())

	ctx := metadata.NewOutgoingContext(stream.Context(), md)
	_, err := gf.grpc_pool.Call(ctx, addr, func(ctx context.Context, addr string, conn *grpc.ClientConn) (any, error) {
		clientStream, err := grpc.NewClientStream(
			ctx,
			&grpc.StreamDesc{ServerStreams: true, ClientStreams: true},
			conn,
			fullMethodName,
		)

		if err != nil {
			return nil, err
		}

		defer func() {
			header, _ = clientStream.Header()
			clientStream.CloseSend()
		}()

		f := newForward(clientStream, stream)

		if isStream {
			err = f.forwardStream()
		} else {
			err = f.forwardUnary()
		}

		return nil, err
	})

	if header.Len() > 0 {
		c.SetStore(plugins.GrpcHeaderKey{}, header)
	}

	if err != nil {
		c.Abort(err)
		return
	}

	return
}

type forward struct {
	clientStream grpc.ClientStream
	serverStream grpc.ServerStream
}

func newForward(clientStream grpc.ClientStream, serverStream grpc.ServerStream) *forward {
	return &forward{
		clientStream: clientStream,
		serverStream: serverStream,
	}
}

func (gf *forward) forwardStream() error {
	ret := make(chan error, 2)
	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer func() {
			wg.Done()
		}()
		//读尽
		for {
			msg := &grpc_pools.Frame{}
			err := gf.serverStream.RecvMsg(msg)
			if err != nil {
				ret <- err
				break
			}

			err = gf.clientStream.SendMsg(msg)
			if err != nil {
				ret <- err
				break
			}
		}
	}()

	go func() {
		defer func() {
			wg.Done()
		}()
		for {
			msg := &grpc_pools.Frame{}
			err := gf.clientStream.RecvMsg(msg)
			if err != nil {
				ret <- err
				break
			}
			err = gf.serverStream.SendMsg(msg)
			if err != nil {
				ret <- err
				break
			}
		}
	}()

	wg.Wait()

	var errReturn error
	for i := 0; i < 2; i++ {
		err := <-ret
		if !errors.Is(err, io.EOF) {
			errReturn = err
		}
	}

	return errReturn
}

func (gf *forward) forwardUnary() error {
	msg_a := &grpc_pools.Frame{}
	err := gf.serverStream.RecvMsg(msg_a)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	err = gf.clientStream.SendMsg(msg_a)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	msg_b := &grpc_pools.Frame{}
	err = gf.clientStream.RecvMsg(msg_b)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	header, _ := gf.clientStream.Header()
	if len(header) > 0 {
		gf.serverStream.SetHeader(header)
	}

	err = gf.serverStream.SendMsg(msg_b)
	if err != nil && !errors.Is(err, io.EOF) {
		return err
	}

	return nil
}
