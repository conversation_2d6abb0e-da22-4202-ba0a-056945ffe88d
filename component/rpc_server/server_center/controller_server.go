package server_center

import (
	"context"
	"fmt"
	rpc_server_wraper "skynet-runtime/component/rpc_server/wrapper"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandorapb "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/ppb"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/tlb"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/tlb/model"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
)

type controllerService struct {
	rpc_server_wraper.RpcServer
	serviceName string
	tlb_sdk     *tlb.TlbSdk
	port        types.Port
	callBacks   map[string]func(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error)
	streamBacks map[string]func(grpc.BidiStreamingServer[pandorapb.RpcRequest, pandorapb.RpcResponse]) error
	pandorapb.UnimplementedSkynetPandoraServiceServer
}

func RegisterControllerServiceCall(rpc rpc_server_wraper.RpcServer, path string, f func(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error)) {
	ls := rpc.(*controllerService)
	ls.callBacks[path] = f
}

func (f *ServerCenterComponent) newControllerServer(port int, metricsUrl string) error {
	console.Console("new controller server", port, metricsUrl)
	ls := &controllerService{
		port:        types.NewServerPort(port),
		callBacks:   map[string]func(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error){},
		streamBacks: map[string]func(grpc.BidiStreamingServer[pandorapb.RpcRequest, pandorapb.RpcResponse]) error{},
	}
	ls.RpcServer = rpc_server_wraper.NewRpcServer(types.ComponentController)
	ls.RpcServer.NewHttpServerWithAddr(ls.port)
	ls.initTlb()

	if metricsUrl != "" {
		console.Console("controller", "register metrics url", metricsUrl)
		ls.HttpServer().Router.GET(metricsUrl, gin.WrapH(promhttp.Handler()))
	}
	f.mSevers[types.ComponentController] = ls
	//f.mSevers[types.ComponentListen].GrpcServer().RegisterService(&ppb.SkynetPandoraService_ServiceDesc, ls)
	return nil
}

func (ls *controllerService) initTlb() error {

	if goboot.BootConf().DefaultConfig().ServiceName != "" {
		ls.serviceName = fmt.Sprintf("%s_skynetmesh", goboot.BootConf().DefaultConfig().ServiceName)
	}

	if goboot.TlbSdk().Enable() && ls.serviceName != "" {
		option := &model.TlbModel{}
		option.UName = ls.serviceName
		option.UEnable = true
		option.ServiceName = ls.serviceName
		option.Servers = goboot.TlbSdk().DefaultConfig().Servers
		option.Host = goboot.TlbSdk().DefaultConfig().Host
		option.Port = ls.port.ToInt()
		option.Maxlics = goboot.TlbSdk().DefaultConfig().Maxlics
		option.HeartbeatInterval = goboot.TlbSdk().DefaultConfig().HeartbeatInterval
		option.CacheInterval = goboot.TlbSdk().DefaultConfig().CacheInterval
		option.TlbTag = goboot.TlbSdk().DefaultConfig().TlbTag
		option.ConnectTimeOutMillSecond = goboot.TlbSdk().DefaultConfig().ConnectTimeOutMillSecond

		reporter, err := tlb.NewInstance(option)
		if err != nil {
			return fmt.Errorf("controller tlb init err: %v", err)
		}

		ls.tlb_sdk = reporter
	}

	return nil
}

func (f *ServerCenterComponent) runControllerServer() error {
	if f.mSevers[types.ComponentController] == nil {
		return nil
	}
	ls := f.mSevers[types.ComponentController].(*controllerService)

	err := ls.HttpServer().RunHttpServer()
	if err != nil {
		console.Console(types.ComponentController, "run listen server failed", err)
		return err
	}

	if ls.tlb_sdk != nil {
		err = ls.tlb_sdk.Register()
		if err != nil {
			return fmt.Errorf("controller tlb register err: %v", err)
		}
	}

	/*
		err = f.mSevers[types.ComponentListen].GrpcServer().RunGrpcServer()
		if err != nil {
			console.Console(types.ComponentListen, "run listen server failed", err)
			return err
		}
	*/

	return nil
}

func (f *ServerCenterComponent) exitControllerServer() {
	if f.mSevers[types.ComponentController] == nil {
		return
	}

	ls := f.mSevers[types.ComponentController].(*controllerService)
	if ls.tlb_sdk != nil {
		ls.tlb_sdk.UnRegister()
	}
}

/*
func (ps *controllerService) RegisterCall(path string, f func(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error)) error {
	if ps.callBacks == nil {
		ps.callBacks = make(map[string]func(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error))
	}

	if _, ok := ps.callBacks[path]; ok {
		return errors.New("path already exist")
	}

	ps.callBacks[path] = f
	return nil
}

func (ps *controllerService) RegisterStream(path string, f func(grpc.BidiStreamingServer[pandorapb.RpcRequest, pandorapb.RpcResponse]) error) error {
	if ps.streamBacks == nil {
		ps.streamBacks = make(map[string]func(grpc.BidiStreamingServer[pandorapb.RpcRequest, pandorapb.RpcResponse]) error)
	}

	if _, ok := ps.callBacks[path]; ok {
		return errors.New("path already exist")
	}

	ps.streamBacks[path] = f
	return nil
}

func (ps *controllerService) Stream(stream grpc.BidiStreamingServer[pandorapb.RpcRequest, pandorapb.RpcResponse]) error {
	md, ok := metadata.FromIncomingContext(stream.Context())
	if !ok {
		return fmt.Errorf("get metadata from context failed")
	}
	var path = ""

	uris := md.Get(rpc_header.RpcUri)

	if len(uris) > 0 {
		ur, err := url.Parse(uris[0])
		if err != nil {
			return fmt.Errorf("parse uri failed %s", err.Error())
		}

		path = ur.Path
	} else {
		paths := md.Get(rpc_header.RpcPath)
		if len(paths) > 0 {
			path = paths[0]
		}
	}

	if path == "" {
		return fmt.Errorf("get path from metadata failed")
	}

	if f, ok := ps.streamBacks[path]; ok {
		return f(stream)
	} else {
		return fmt.Errorf("path not found %s", path)
	}
}

func (ps *controllerService) Call(ctx context.Context, pb_req *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error) {

	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, fmt.Errorf("get metadata from context failed")
	}
	var path = ""

	uris := md.Get(rpc_header.RpcUri)

	if len(uris) > 0 {
		ur, err := url.Parse(uris[0])
		if err != nil {
			return nil, fmt.Errorf("parse uri failed %s", err.Error())
		}

		path = ur.Path
	} else {
		paths := md.Get(rpc_header.RpcPath)
		if len(paths) > 0 {
			path = paths[0]
		}
	}

	if path == "" {
		return nil, fmt.Errorf("get path from metadata failed")
	}

	if f, ok := ps.callBacks[path]; ok {
		return f(ctx, pb_req)
	} else {
		return nil, fmt.Errorf("path not found %s", path)
	}
}

func (ps *controllerService) Ping(context.Context, *pandorapb.RpcRequest) (*pandorapb.RpcResponse, error) {
	return nil, errors.New("unsupport temporary")
}
*/
