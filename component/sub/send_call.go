package sub

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"skynet-runtime/internal/console"
	"sync"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_client"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/kafka"
	"github.com/Shopify/sarama"
	"github.com/bytedance/sonic"
)

type sendCall struct {
	key     string
	subName string
	si      subInput
	client  *client
	c       *kafka.ConsumerGroupHandler
	status  error
	rwlock  sync.RWMutex
	ctx     context.Context
	cancel  context.CancelCauseFunc
}

func (sc *sendCall) process() func(session sarama.ConsumerGroupSession, mesg *sarama.ConsumerMessage) {
	return func(session sarama.ConsumerGroupSession, mesg *sarama.ConsumerMessage) {
		//sub_metrics.StatsTotal(sc.key, 1)
		data := &subOutput{
			Headers: map[string]string{},
		}

		for _, v := range mesg.Headers {
			data.Headers[string(v.Key)] = string(v.Value)
		}

		data.BlockTimestamp = mesg.BlockTimestamp
		data.Key = string(mesg.Key)
		data.Timestamp = mesg.Timestamp
		data.Offset = mesg.Offset
		data.Partition = mesg.Partition
		data.Value = string(mesg.Value)

		s, err := sonic.Marshal(data)
		if err != nil {
			//sub_metrics.StatsError(sc.key, 1)
			g_logger.Error(console.Fmt("sub", "consumer marshal data error", data.Partition, data.Offset, err))
			return
		} else {
			for {
				if sc.ctx.Err() != nil {
					console.Console("sub", "ctx error return", sc.key, data.Partition, data.Offset, sc.ctx.Err())
					return
				}
				err := sc.client.send(s)
				if err != nil {
					//sub_metrics.StatsError(sc.key, 1)
					g_logger.Error(console.Fmt("sub", "send consumer fail", sc.key, data.Partition, data.Offset, err))
					time.Sleep(time.Duration(1) * time.Second)
				} else {
					break
				}
			}

			g_logger.Info(console.Fmt("sub", "consumer success", sc.key, data.Partition, data.Offset, err))
			session.MarkMessage(mesg, "")
		}
	}
}

// 获取当前状态
func (sc *sendCall) GetStatus() error {
	sc.rwlock.RLock()
	defer sc.rwlock.RUnlock()
	return sc.status
}

// 设置当前状态
func (sc *sendCall) SetStatus(err error) {
	sc.rwlock.Lock()
	defer sc.rwlock.Unlock()
	sc.status = err
}

type client struct {
	httpClient *http.Client
	si         *subInput
}

func newClient(si *subInput) (*client, error) {
	c := &client{
		si: si,
	}

	switch si.Proto {
	case "http":
		if si.IsUnix {
			// Unix socket 文件路径
			socketPath := si.Addr

			// 创建自定义 Transport，使用 Unix 域套接字
			tr := &http.Transport{
				Dial: func(network, addr string) (net.Conn, error) {
					return net.Dial("unix", socketPath)
				},
			}

			// 创建 HTTP 客户端，使用自定义 Transport
			c.httpClient = &http.Client{
				Transport: tr,
			}
		} else {
			c.httpClient = http.DefaultClient
		}

	case "grpc":
	default:
		return nil, errors.New("not support proto")
	}
	return c, nil
}

func (c *client) send(data []byte) error {
	rc := rpc_client.NewRpcClient()
	si := c.si

	if si.IsUnix {
		rc.SetAddr("unix://" + si.Addr)
	} else {
		rc.SetAddr(si.Addr)
	}

	if si.Proto == "http" {
		rc.SetHttpClient(c.httpClient)
	}

	rc.SetPath(si.Path)

	rc.Input = data

	if si.Proto == "http" {
		rc.Post(context.TODO())
	} else {
		rc.Grpc(context.TODO())
	}

	err := rc.Error()
	if err != nil {
		return err
	}

	if rc.StatusCode != 0 && rc.StatusCode != 200 {
		return fmt.Errorf("status code %d", rc.StatusCode)
	}
	return nil
}
