package sub

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/logger"
	"go.uber.org/zap"
)

type loggerConf struct {
	Pure      bool   `toml:"pure" default:"false"` //pure 模式，不会添加任何额外的信息，只打印业务日志
	Fpath     string `toml:"file_path" default:"./logs/component_sub.log"`
	Level     string `toml:"level"`
	MaxSizeMb int    `toml:"max_size_mb" default:"100"`
	MaxBackUp int    `toml:"max_back_up" default:"30"`
	MaxAge    int    `toml:"max_age_day" default:"90"`
	Compress  bool   `toml:"compress" default:"false"`
	Console   bool   `toml:"console" default:"false"`
}

var g_logger *zap.Logger

func initLogger(lc *loggerConf) error {
	option := &logger.LogModel{
		Fpath:     lc.Fpath,
		Level:     lc.Level,
		MaxSizeMb: lc.MaxSizeMb,
		MaxBackUp: lc.Max<PERSON>ack<PERSON>,
		MaxAge:    lc.Max<PERSON>ge,
		Compress:  lc.Compress,
		Console:   lc.<PERSON>sole,
		Pure:      lc.Pure,
	}

	option.UEnable = true
	option.UName = "component_sub"
	var err error
	g_logger, err = logger.NewInstance(option)

	if err != nil {
		return err
	}
	return nil
}
