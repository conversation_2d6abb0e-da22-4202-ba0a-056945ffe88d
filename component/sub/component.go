package sub

import (
	"context"
	"encoding/json"
	"net/http"
	"skynet-runtime/component/rpc_server/server_center"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/ppb"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"github.com/gin-gonic/gin"
)

type SubComponent struct {
	conf   *confWrapper
	cae    *subCache
	rs_sub *rpc_server2.RpcServer
	types.DefaultComponent
}

var g_sub *SubComponent = &SubComponent{}

func Component() types.Component {
	return g_sub
}

func (pc *SubComponent) Name() types.ComponentType {
	return types.ComponentSub
}

func (pc *SubComponent) Init() error {

	level := "info"
	if v, ok := goboot.BootConf().DefaultConfig().Lables["log_level"]; ok {
		level = v.(string)
	}

	temp := &subConfWrapper{
		Sub: confWrapper{
			Values:  map[string]subConf{},
			Metrics: true,
			Logger: loggerConf{
				Pure:      false,
				Fpath:     "./logs/component_sub.log",
				Level:     level,
				MaxSizeMb: 100,
				MaxBackUp: 30,
				MaxAge:    90,
				Compress:  false,
				Console:   false,
			},
		},
	}

	err := goboot.UnmarshalWithConfigAndEnv(types.ComponentSub.EnvName(), temp)
	if err != nil {
		return err
	}

	s, _ := json.Marshal(temp)
	console.Console(types.ComponentSub.ToString(), "config", string(s))

	pc.conf = &temp.Sub
	//sub_metrics.Init(pc.conf.Metrics)

	console.Console("sub", "metrics", pc.conf.Metrics)

	err = initLogger(&pc.conf.Logger)
	if err != nil {
		return err
	}

	if len(pc.conf.Values) == 0 {
		console.Console("sub", "no sub config")
		return nil
	}

	pc.rs_sub = rpc_server2.NewRpcServer()
	pc.rs_sub.AddFunc("plugin_header", plugins.SkynetHeaderPlugin(types.ComponentSub))
	pc.rs_sub.AddFunc("io_read", plugins.IoReader)
	pc.rs_sub.AddFunc("pub", pc.subScribe)

	pc.cae = newSubCache()

	for name, conf := range pc.conf.Values {
		console.Console("sub", name, conf.Type, conf.Kafka)
	}

	return nil
}

func (pc *SubComponent) Run() error {
	//路由注册
	pc.registerServer()
	return nil
}

func (pc *SubComponent) registerServer() {
	console.Console("sub", "http router", "/api/v1/sub")
	r := server_center.GetRpcServer(types.ComponentController)

	r.HttpServer().Router.POST("/api/v1/sub", func(ctx *gin.Context) {
		rctx := rpc_server2.NewRpcServerContext()
		_, err := pc.rs_sub.GinStart(ctx, rctx)
		if err != nil {
			g_logger.Error(console.Fmt("sub", "subScribe error", err))
			ctx.String(http.StatusInternalServerError, err.Error())
			return
		}
		ctx.String(http.StatusOK, string(rctx.Output))
	})

	console.Console("sub", "grpc router", "/api/v1/sub")
	server_center.RegisterControllerServiceCall(r, "/api/v1/sub", func(ctx context.Context, pb_req *ppb.RpcRequest) (*ppb.RpcResponse, error) {
		rctx := rpc_server2.NewRpcServerContext()
		rctx.Input = pb_req.Data
		_, err := pc.rs_sub.GrpcStart(ctx, rctx)

		return &ppb.RpcResponse{
			Data: rctx.Output,
		}, err
	})
}
