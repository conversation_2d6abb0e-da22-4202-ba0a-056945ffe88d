package sub

import (
	"fmt"
	"sync"
)

type subCache struct {
	rwlock sync.RWMutex
	c      map[string]*sendCall
}

func newSubCache() *subCache {
	// 初始化一个新的subCache实例
	return &subCache{
		rwlock: sync.RWMutex{},
		c:      make(map[string]*sendCall),
	}
}

// 添加缓存项
func (sc *subCache) Add(key string, se *sendCall) error {
	sc.rwlock.Lock()
	defer sc.rwlock.Unlock()

	// 如果已存在则先取消旧的context
	if _, exists := sc.c[key]; exists {
		return fmt.Errorf("key %s already exists", key)
	}

	sc.c[key] = se
	return nil
}

// 获取缓存项
func (sc *subCache) Get(key string) (*sendCall, bool) {
	sc.rwlock.RLock()
	defer sc.rwlock.RUnlock()

	c, exists := sc.c[key]
	return c, exists
}

// 删除缓存项
func (sc *subCache) Delete(key string) *sendCall {
	sc.rwlock.Lock()
	defer sc.rwlock.Unlock()

	// 如果存在则取消context并删除
	if c, exists := sc.c[key]; exists {
		delete(sc.c, key)
		return c
	}

	return nil
}
