package sub

import (
	"context"
	"errors"
	"fmt"
	"skynet-runtime/internal/console"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/kafka"
	"github.com/bytedance/sonic"
)

type subOutput struct {
	Headers        map[string]string // only set if kafka is version 0.11+
	Timestamp      time.Time         // only set if kafka is version 0.10+, inner message timestamp
	BlockTimestamp time.Time         // only set if kafka is version 0.10+, outer (compressed) block timestamp

	Key, Value string
	Topic      string
	Partition  int32
	Offset     int64
}

type subInput struct {
	SubName string `json:"sub_name"`
	Action  string `json:"action"` //cancel or new
	IsUnix  bool   `json:"unix"`
	Proto   string `json:"proto"` //http or grpc or uinx
	Addr    string `json:"addr"`  //回调地址：0.0.0.0:7788 or /tmp/xxx.sock
	Path    string `json:"path"`
}

func (si subInput) uniqueKey(subName string) string {
	return fmt.Sprintf("%s:%s:%s:%s", subName, si.Proto, si.Addr, si.Path)
}

func (pc *SubComponent) subScribe(c chains_engine.ChainsContext) {
	rs := rpc_server2.GetRpcServerContextFromContext(c)
	si := &subInput{}
	err := sonic.Unmarshal(rs.Input, si)

	if err != nil {
		c.Abort(fmt.Errorf("unmarshal input fail %s", err.Error()))
		return
	}

	subName := si.SubName
	_, ok := pc.conf.Values[subName]
	if !ok {
		c.Abort(fmt.Errorf("sub fail: invalid sub name %s", subName))
		return
	}

	uikey := si.uniqueKey(subName)

	switch si.Action {
	case "new":

		consumer, err := pc.createConsumer(subName)
		if err != nil {
			c.Abort(fmt.Errorf("create consumer fail:%s", err.Error()))
			return
		}

		client, err := newClient(si)
		if err != nil {
			c.Abort(fmt.Errorf("create client error %s", err.Error()))
			return
		}

		ctx, cancel := context.WithCancelCause(context.TODO())

		sc := &sendCall{
			key:     uikey,
			subName: subName,
			si:      *si,
			c:       consumer,
			ctx:     ctx,
			cancel:  cancel,
			client:  client,
		}

		err = pc.cae.Add(uikey, sc)
		if err != nil {
			c.Abort(fmt.Errorf("add cache fail:%s", err.Error()))
			return
		}

		consumer.SetContxt(ctx)
		consumer.SetProcessor(sc.process())

		go func() {
			err := consumer.Run()
			sc.SetStatus(err)
			console.Console("sub", "consumer end", uikey, err)
		}()
		rs.Output = []byte("success")

	case "cancel":
		si := pc.cae.Delete(uikey)

		if si != nil {
			rs.Output = []byte("suucess")
			console.Console("sub", "delete", uikey)
			si.cancel(errors.New("cancel by cancel api"))
		} else {
			c.Abort(fmt.Errorf("not find sub,unikey %s", uikey))
		}
	case "status":
		si, ok := pc.cae.Get(uikey)
		if ok {
			if err = si.GetStatus(); err != nil {
				rs.Output = []byte("stop for:" + err.Error())
				return
			} else {
				rs.Output = []byte("running")
				return
			}
		}

		c.Abort(errors.New("not find"))
		return
	default:
		c.Abort(fmt.Errorf("sub fail invalid action %s", si.Action))
	}
}

func (pc *SubComponent) createConsumer(subName string) (*kafka.ConsumerGroupHandler, error) {
	cv, ok := pc.conf.Values[subName]

	if !ok {
		return nil, fmt.Errorf("sub fail: invalid sub name %s", subName)
	}

	opt := &kafka.KafkaConsumerModel{}
	opt.UEnable = true
	opt.UName = subName
	opt.Brokers = cv.Kafka.Brokers
	opt.Topics = cv.Kafka.Topics
	opt.Group = cv.Kafka.Group
	opt.FetchDefault = cv.Kafka.FetchDefault
	opt.AutoCommit = cv.Kafka.AutoCommit
	opt.Offset = cv.Kafka.Offset
	consumer, err := kafka.NewConsumerInstance(opt)
	if err != nil {
		return nil, errors.New("new consumer instance failed:" + err.Error())
	}

	return consumer, nil
}
