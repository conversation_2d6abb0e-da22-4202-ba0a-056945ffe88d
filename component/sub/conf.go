package sub

type kafkaConf struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Group        string `json:"group" toml:"group"`
	Topics       string `json:"topic" toml:"topic"`
	Offset       string `json:"offset" toml:"offset" default:"oldest"`
	AutoCommit   bool   `json:"auto_commit" toml:"auto_commit" default:"true"`
	FetchDefault int    `json:"fetch_default" toml:"fetch_default" default:"1"` //mb
}

type subConf struct {
	Type  string    `json:"type" toml:"type" default:"kafka"`
	Kafka kafkaConf `json:"kafka" toml:"kafka"`
}

type confWrapper struct {
	Values  map[string]subConf `json:"values" toml:"values"`
	Metrics bool               `json:"metrics" toml:"metrics" default:"true"`
	Logger  loggerConf         `json:"log" toml:"log"`
}

type subConfWrapper struct {
	Sub confWrapper `json:"sub" toml:"sub"`
}
