package discovery

import (
	"context"

	"skynet-runtime/component/rpc_server/server_center"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/plugins"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"github.com/gin-gonic/gin"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/ppb"
)

type DiscoveryComponent struct {
	types.Component
	rs *rpc_server2.RpcServer
}

var g_discovery *DiscoveryComponent = &DiscoveryComponent{}

func Component() types.Component {
	return g_discovery
}
func (c *DiscoveryComponent) Init() error {
	c.Component = types.NewDefaultComponent(types.ComponentDiscovery)
	c.rs = rpc_server2.NewRpcServer()

	c.rs.AddFunc("app header", plugins.SkynetHeaderPlugin(types.ComponentDiscovery))
	c.rs.AddFunc("tlb", plugins.ResolverTlb)
	c.initHttpApi()
	return nil
}

func (dc *DiscoveryComponent) initHttpApi() {
	rpcs := server_center.GetRpcServer(types.ComponentController)

	if rpcs == nil {
		return
	}

	console.Console("discovery", "http router", "/api/v1/discovery")
	r := rpcs.HttpServer().Router

	// 监听所有 HTTP 方法的所有路径
	r.POST("/api/v1/discovery", func(c *gin.Context) {
		rctx := rpc_server2.NewRpcServerContext()
		runCtx, err := dc.rs.GinStart(c, rctx)

		if err != nil {
			c.String(500, err.Error())
		} else {
			out := plugins.GetResolverFromContext(runCtx)
			c.String(200, out)
		}
	})
}

func (dc *DiscoveryComponent) initGrpcApi() {
	console.Console("discovery", "grpc router", "/api/v1/discovery")

	rpcs := server_center.GetRpcServer(types.ComponentController)

	server_center.RegisterControllerServiceCall(rpcs, "/api/v1/discovery", func(ctx context.Context, pb_req *ppb.RpcRequest) (*ppb.RpcResponse, error) {
		rctx := rpc_server2.NewRpcServerContext()
		rctx.Input = pb_req.Data
		rctx.Runtime.Extra["input_len"] = len(pb_req.Data)

		runCtx, err := dc.rs.GrpcStart(ctx, rctx)
		out := plugins.GetResolverFromContext(runCtx)
		return &ppb.RpcResponse{
			Data: []byte(out),
		}, err
	})
}

func Register() error {

	if !goboot.TlbSdk().Enable() {
		return nil
	}

	err := goboot.TlbSdk().DefaultInstance().Register()

	if err != nil {
		return err
	}

	return nil
}

func UnRegister() {
	bif.Cond(goboot.TlbSdk().DefaultInstance() != nil, func() {
		console.Console("discovery", "tlb unregister")
		goboot.TlbSdk().DefaultInstance().UnRegister()
	})
}
