package file_beat

import (
	"errors"
	"fmt"

	"github.com/hpcloud/tail"
	"go.uber.org/zap"
)

type FileBeat struct {
	log         *zap.Logger
	model       FileBeatModel
	fileMonitor *tail.Tail
	sender      *sender
}

type FileBeatConf struct {
	FilePath string `toml:"file_path" default:"/skynet-runtime/logs/app.json"`
}

func NewFileBeat(m FileBeatModel, log *zap.Logger) (*FileBeat, error) {
	fb := &FileBeat{
		model: m,
		log:   log,
	}

	if m.FilePath == "" {
		return nil, errors.New("invalid file path")
	}

	if m.ThreadsNum <= 0 {
		return nil, errors.New("invalid threads num")
	}

	var err error
	fb.sender, err = newSender(&m, log)
	if err != nil {
		return nil, fmt.Errorf("filebeat new sender error: %v", err)
	}

	// 配置tail
	config := tail.Config{
		Follow:    true, // 跟随文件，监控新增内容
		ReOpen:    true, // 文件被截断后重新打开
		Poll:      true, // 使用轮询模式
		MustExist: false,
		Logger:    newTailLog(log),
		Location:  &tail.SeekInfo{Offset: 0, Whence: 2},
	}

	// 使用tail.TailFile创建一个Tail对象
	tailObj, err := tail.TailFile(m.FilePath, config)
	if err != nil {
		return nil, fmt.Errorf("filebeat new tail error: %v", err)
	}

	fb.fileMonitor = tailObj

	go fb.Run()
	return fb, nil
}

func (fb *FileBeat) Run() {
	for line := range fb.fileMonitor.Lines {
		fb.sender.Send(line.Text)
	}
	return
}

/*
func (fb *FileBeat) Monitor() {
	fb.fileMonitor.Monitor(watchFileFunc(fb.sender))
}

type monitor struct {
	path    string
	watcher *fsnotify.Watcher
	log     *zap.Logger
}

func newMonitor(path string, log *zap.Logger) (*monitor, error) {
	m := &monitor{
		path:    path,
		watcher: nil,
		log:     log,
	}

	var err error
	m.watcher, err = fsnotify.NewWatcher()

	return m, err
}

func (m *monitor) Monitor(fcall func(m *monitor) error) {
	for {
		m.watcher.Remove(m.path)
		for {
			err := m.watcher.Add(m.path)
			if err != nil {
				m.log.Error("filebeat", zap.String("path", m.path), zap.Error(err))
				m.watcher.Remove(m.path)
			} else {
				break
			}

			time.Sleep(1 * time.Second)
		}

		m.log.Info("filebeat", zap.String("path", m.path), zap.String("info", "add path success"))

		err := fcall(m)
		m.log.Error(types.ComponentFileBeat.ToString(), zap.Any("error", err))
	}
}

func watchFileFunc(sender *sender) func(m *monitor) error {
	return func(m *monitor) (err error) {
		defer func() {
			console.Console("filebeat", "watch file func exit", err)
		}()
		for {
			select {
			case event, ok := <-m.watcher.Events:
				if !ok {
					return errors.New("filebeat watch chan is closed")
				}
				log.Println("事件:", event.Name, event.Op.String())

				if event.Op == fsnotify.Write {
					//log.Println("文件被修改:", event.Name)
					//fmt
				} else {
					return fmt.Errorf("filebeat watch event %s %s", event.Name, event.String())
				}

			case err, ok := <-m.watcher.Errors:
				if !ok {
					return fmt.Errorf("filebeat watch chan is closed")
				}

				m.log.Error("filebeat", zap.String("path", m.path), zap.Error(err))
			}
		}
	}
}
*/
