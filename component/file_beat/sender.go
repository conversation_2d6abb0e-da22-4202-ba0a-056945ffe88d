package file_beat

import (
	"fmt"
	"strings"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/fixpool"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/kafka"
	"go.uber.org/zap"
)

type sender struct {
	producer *kafka.ProducerHandler
	fixpool  *fixpool.AsyncFixPool
	log      *zap.Logger
}

func newSender(m *FileBeatModel, log *zap.Logger) (*sender, error) {
	km := &kafka.KafkaProducerModel{}
	km.UEnable = true
	km.UName = fmt.Sprintf("filebeat_%s", m.FilePath)
	km.Brokers = m.Kafka.Brokers
	km.Topic = m.Kafka.Topic
	km.Gzip = m.Kafka.Gzip
	km.Partitioner = m.Kafka.Partitioner
	km.RequestMaxMb = m.Kafka.RequestMaxMb

	producer, err := kafka.NewProducerInstance(km)

	if err != nil {
		return nil, err
	}

	fm := fixpool.FixPoolModel{}
	fm.UEnable = true
	fm.UName = fmt.Sprintf("filebeat_%s", m.FilePath)
	fm.WorkNum = m.ThreadsNum
	fm.QueueNum = 1024
	fp := fixpool.NewFixTasksPool(&fm)

	return &sender{
		fixpool:  fp,
		producer: producer,
		log:      log,
	}, nil
}

func (s *sender) Send(text string) {
	s.fixpool.SubmitBlock(func() (any, error) {

		for {
			bExit := true
			if strings.HasSuffix(text, "\n") {
				bExit = false
				text = strings.TrimSuffix(text, "\n")
			}

			if strings.HasSuffix(text, "\r") {
				bExit = false
				text = strings.TrimSuffix(text, "\r")
			}

			if strings.HasSuffix(text, "\r\n") {
				bExit = false
				text = strings.TrimSuffix(text, "\r\n")
			}

			if bExit {
				break
			}
		}

		partion, offset, err := s.producer.SyncMessage([]byte(text), nil, nil)
		if err != nil {
			s.log.Error("send message failed", zap.Error(err), zap.Any("brokers", s.producer.Brokers), zap.String("topic", s.producer.Topic), zap.Int32("partion", partion), zap.Int64("offset", offset))
		}

		return nil, nil
	})
}
