package file_beat

import (
	"encoding/json"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

type FileBeatComponent struct {
	types.Component
	fbs []*FileBeat
}

var g_filebeat *FileBeatComponent = &FileBeatComponent{}

func Component() types.Component {
	return g_filebeat
}

func (pc *FileBeatComponent) Init() error {
	pc.Component = types.NewDefaultComponent(types.ComponentFileBeat)

	gw := &FileBeatConfWrapper{}

	err := goboot.UnmarshalWithConfigAndEnv(types.ComponentFileBeat.EnvName(), gw)
	if err != nil {
		return err
	}

	s, _ := json.Marshal(gw)
	console.Console(types.ComponentFileBeat.ToString(), "config", string(s))

	results := make([]FileBeatModel, 0, len(gw.Fbm))

	for _, v := range gw.Fbm {
		if v.Enabled {
			results = append(results, v)
		}
	}

	gw.Fbm = results

	if len(gw.Fbm) == 0 {
		console.Console("file beat", "config is len zero")
		return nil
	}

	pc.fbs = make([]*FileBeat, 0, len(gw.Fbm))
	for _, v := range gw.Fbm {
		f, err := NewFileBeat(v, pc.Log())
		if err != nil {
			return err
		}

		pc.fbs = append(pc.fbs, f)
		console.Console(types.ComponentFileBeat.ToString(), "success", f.model.FilePath)
	}

	return nil
}

func (pc *FileBeatComponent) Run() error {
	return nil
}
