package file_beat

import (
	"fmt"

	"go.uber.org/zap"
)

type tailLog struct {
	log *zap.Logger
}

func newTailLog(logger *zap.Logger) *tailLog {
	return &tailLog{log: logger}
}

func (t *tailLog) Print(v ...interface{}) {
	t.log.Info(fmt.Sprint(v...))
}

func (t *tailLog) Printf(format string, v ...interface{}) {
	t.log.Info(fmt.Sprintf(format, v...))
}

func (t *tailLog) Println(v ...interface{}) {
	t.log.Info(fmt.Sprintln(v...))
}

func (t *tailLog) Fatal(v ...interface{}) {
	t.log.Fatal(fmt.Sprint(v...))
}

func (t *tailLog) Fatalf(format string, v ...interface{}) {
	t.log.Fatal(fmt.Sprintf(format, v...))
}

func (t *tailLog) Fatalln(v ...interface{}) {
	t.log.Fatal(fmt.Sprintln(v...))
}

func (t *tailLog) Panic(v ...interface{}) {
	t.log.Panic(fmt.Sprint(v...))
}

func (t *tailLog) Panicf(format string, v ...interface{}) {
	t.log.Panic(fmt.Sprintf(format, v...))
}

func (t *tailLog) Panicln(v ...interface{}) {
	t.log.Panic(fmt.Sprintln(v...))
}
