package file_beat

type kafkaConf struct {
	Brokers      string `json:"brokers" toml:"brokers"`
	Topic        string `json:"topic" toml:"topic"`
	Partitioner  string `json:"partitioner" toml:"partitioner" default:"rr"`
	RequestMaxMb int    `json:"request_maxmb" toml:"request_maxmb" default:"10"`
	Gzip         bool   `json:"gzip" toml:"gzip" default:"false"`
}

type FileBeatModel struct {
	Enabled    bool      `json:"enabled" toml:"enabled" default:"false"`
	FilePath   string    `json:"file_path" toml:"file_path" default:"/skynet-runtime/logs/app.json"`
	ThreadsNum int       `json:"threads_num" toml:"threads_num" default:"5"`
	QueueNum   int       `json:"queue_num" toml:"queue_num" default:"1024"`
	Kafka      kafkaConf `json:"kafka" toml:"kafka"`
}

type FileBeatConfWrapper struct {
	Fbm []FileBeatModel `json:"file_beat" toml:"file_beat"`
}
