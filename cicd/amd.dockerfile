# 第一阶段：使用Go镜像来构建应用程序
FROM artifacts.iflytek.com/docker-private/datahub/golang:1.23.4-arm64 AS builder

# 设置工作目录
WORKDIR /skynet

# 复制所有文件到工作目录
COPY . .
RUN ls
RUN lscpu 
RUN go version
RUN go env -w GOPROXY=https://goproxy.cn,direct
# 下载依赖并构建 Go 应用程序
RUN go build -tags=process -o ./output/skynet-mesh

# 第二阶段：创建一个更小的运行时镜像,添加了libssl依赖
FROM artifacts.iflytek.com/docker-private/datahub/medsearch-ubuntu22-npu:v1.0.1-arm64

# 设置工作目录
WORKDIR /skynet

# 从第一阶段复制构建的应用程序二进制文件
COPY --from=builder /skynet/output/skynet-mesh .