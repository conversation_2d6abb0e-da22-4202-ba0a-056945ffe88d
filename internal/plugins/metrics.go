package plugins

import (
	"skynet-runtime/internal/mmetrics"
	"skynet-runtime/internal/skynet_proto"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
	"google.golang.org/grpc/metadata"
)

func ForwardMetrics() func(c chains_engine.ChainsContext) {
	return func(c chains_engine.ChainsContext) {
		app_header := GetSkynetAppFromContxt(c)
		server := app_header.AppServer
		rs := rpc_server2.GetRpcServerContextFromContext(c)

		mmetrics.ForwardTotalCounter.CounterInc(1, server, rs.Path, rs.ServerType())
		mmetrics.ForwardConcurrency.GuageInc(1, server, rs.Path, rs.ServerType())

		st := time.Now()
		c.Next()

		mmetrics.ForwardConcurrency.GuageDec(1, server, rs.Path, rs.ServerType())

		if c.Error() != nil {
			mmetrics.ForwardStatusCounter.CounterInc(1, server, rs.Path, rs.ServerType(), "1000")
		} else {
			status := "200"
			if rs.GrpcContext != nil {
				hmd, ok := c.GetStore(GrpcHeaderKey{})
				if ok {
					header := hmd.(metadata.MD)
					if len(header) > 0 {
						statusCode, ok := header[skynet_proto.AppHeaderStatusCode]
						if ok && statusCode[0] != "" && statusCode[0] != "0" && statusCode[0] != "200" {
							status = statusCode[0]
						}
					}
				}
			}

			if rs.GinContext != nil {
				statusCode, ok := c.GetStore(HttpStatusKey{})
				if ok {
					status = statusCode.(string)
				}
			}

			mmetrics.ForwardStatusCounter.CounterInc(1, server, rs.Path, rs.ServerType(), status)
		}

		mmetrics.ForwardLatency.LatencyObserve(float64(time.Now().Sub(st).Milliseconds()),
			server, rs.Path, rs.ServerType())

		return
	}
}

func HijackMetrics() func(c chains_engine.ChainsContext) {
	return func(c chains_engine.ChainsContext) {
		rs := rpc_server2.GetRpcServerContextFromContext(c)

		mmetrics.HijackTotalCounter.CounterInc(1, rs.Path, rs.ServerType())
		mmetrics.HijackConcurrency.GuageInc(1, rs.Path, rs.ServerType())

		st := time.Now()
		c.Next()

		mmetrics.HijackConcurrency.GuageDec(1, rs.Path, rs.ServerType())

		if c.Error() != nil {
			mmetrics.HijackStatusCounter.CounterInc(1, rs.Path, rs.ServerType(), "1000")
		} else {
			status := "200"
			if rs.GrpcContext != nil {
				hmd, ok := c.GetStore(GrpcHeaderKey{})
				if ok {
					header := hmd.(metadata.MD)
					if len(header) > 0 {
						statusCode, ok := header[skynet_proto.AppHeaderStatusCode]
						if ok && statusCode[0] != "" && statusCode[0] != "0" && statusCode[0] != "200" {
							status = statusCode[0]
						}
					}
				}
			}

			if rs.GinContext != nil {
				statusCode, ok := c.GetStore(HttpStatusKey{})
				if ok {
					status = statusCode.(string)
				}
			}

			mmetrics.HijackStatusCounter.CounterInc(1, rs.Path, rs.ServerType(), status)
		}

		mmetrics.HijackLatency.LatencyObserve(float64(time.Now().Sub(st).Milliseconds()),
			rs.Path, rs.ServerType())

		return
	}
}
