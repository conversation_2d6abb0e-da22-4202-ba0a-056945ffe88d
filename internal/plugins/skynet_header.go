package plugins

import (
	"skynet-runtime/internal/skynet_proto"
	"skynet-runtime/internal/types"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
)

type Grpc<PERSON>eader<PERSON>ey struct{}
type HttpStatusKey struct{}

func SkynetHeaderPlugin(name types.ComponentType) func(chains_engine.ChainsContext) {
	return func(c chains_engine.ChainsContext) {
		rs := rpc_server2.GetRpcServerContextFromContext(c)
		app := skynet_proto.NewSkynetAppFromHttpHeader(rs.RequestHeader)
		c.SetStore(skynet_proto.SkynetAppKey{}, app)
		c.SetStore(types.ComponentKey{}, name)
	}
}

func GetSkynetAppFromContxt(c chains_engine.ChainsContext) *skynet_proto.SkynetApp {
	return c.GetStoreWithDef(skynet_proto.SkynetAppKey{}, nil).(*skynet_proto.SkynetApp)
}
