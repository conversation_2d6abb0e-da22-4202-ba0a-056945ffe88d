package plugins

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	plugin_center "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/plugins/center"
	ioread_plugin "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/plugins/io_read"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/rpc_server2"
)

func IoReader(c chains_engine.ChainsContext) {
	rs := rpc_server2.GetRpcServerContextFromContext(c)
	if rs.GinContext != nil {
		f := plugin_center.GetPlugin(ioread_plugin.IOReadPluginName).PluginFunc()
		f(c)
	}
}
