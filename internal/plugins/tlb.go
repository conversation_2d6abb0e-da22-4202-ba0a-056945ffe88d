package plugins

import (
	"errors"
	"fmt"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/chains_engine"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/tlb/model"
)

type ResolverKey struct{}

func GetResolverFromContext(c chains_engine.ChainsContext) string {
	v, ok := c.GetStore(ResolverKey{})
	if !ok {
		panic("not find Resolver")
	}

	return v.(string)
}

func ResolverTlb(c chains_engine.ChainsContext) {
	if !goboot.TlbSdk().Enable() {
		c.Abort(errors.New("tlb not enable"))
		return
	}

	//goboot.TlbSdk().DefaultInstance().AddUsedLic(1)
	app_header := GetSkynetAppFromContxt(c)
	server := app_header.AppServer
	tag := app_header.AppTag

	if tag == "" {
		tag = goboot.TlbSdk().DefaultConfig().TlbTag
	}

	var info *model.ServerInfo
	var err error
	if tag == "" {
		info, err = goboot.TlbSdk().DefaultInstance().GetBestService(c.Context(), server)
	} else {
		info, err = goboot.TlbSdk().DefaultInstance().GetBestServiceEx(c.Context(), server, tag)
	}

	if err != nil {
		c.Abort(fmt.Errorf("tlb get best service err: %v", err))
		return
	}

	c.SetStore(ResolverKey{}, info.Addr())

	c.Next()

	//goboot.TlbSdk().DefaultInstance().DescUsedLic(1)
	return
}
