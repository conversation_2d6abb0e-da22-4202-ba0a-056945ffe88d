package runtime_error

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/structure/unsafe/berror"
)

var (
	RuntimeError   = NewRuntimeMeshError(1000, "runtime mesh frame run fail")
	ParamError     = NewRuntimeMeshError(1001, "param parse fail")
	PanicError     = NewRuntimeMeshError(1002, "server painc error")
	ResultNil      = NewRuntimeMeshError(1003, "server response nil")
	MarshalFailed  = NewRuntimeMeshError(1004, "server  marshal failed")
	GzipFailed     = NewRuntimeMeshError(1005, "server  gip failed")
	MethodNotFound = NewRuntimeMeshError(1006, "path not found")
)

type RuntimeMeshError = berror.BError

func NewRuntimeMeshError(code int, msg string) *RuntimeMeshError {
	return berror.NewBError(code, msg)
}
