package skynet_proto

import (
	"net/http"

	"google.golang.org/grpc/metadata"
)

const (
	AppHeaderTraceId    = "skynet-app-traceId"
	AppHeaderMethod     = "skynet-app-method"
	AppHeaderGrpcType   = "skynet-app-grpc-type"
	AppHeaderUri        = "skynet-app-uri"
	AppHeaderServer     = "skynet-app-server"
	AppHeaderClient     = "skynet-app-client"
	AppHeaderReqTag     = "skynet-app-tag"
	AppHeaderIp         = "skynet-app-ip"
	AppHeaderStatusCode = "skynet-app-status-code"
	AppHeaderForward    = "skynet-forward-header"
)

type SkynetAppKey struct{}

// TODO: tlb tag 没有实现
type SkynetApp struct {
	//AppTraceId string
	//AppMethod   string
	//AppUri      string
	AppServer   string
	AppGrpcType string
	//AppClient  string
	AppTag string
	//AppIp  string
}

func NewSkynetAppFromHttpHeader(h http.Header) *SkynetApp {
	sk := &SkynetApp{}
	//sk.AppTraceId = h.Get(AppHeaderTraceId)
	//sk.AppMethod = h.Get(AppHeaderMethod)
	//sk.AppUri = h.Get(AppHeaderUri)
	sk.AppServer = h.Get(AppHeaderServer)
	sk.AppGrpcType = h.Get(AppHeaderGrpcType)
	//sk.AppClient = h.Get(AppHeaderClient)
	sk.AppTag = h.Get(AppHeaderReqTag)
	//sk.AppIp = h.Get(AppHeaderIp)
	return sk
}

func NewSkynetAppFromGrpcHeader(md metadata.MD) *SkynetApp {
	//先转http header
	h := http.Header{}

	for k, v := range md {
		h.Set(k, v[0])
	}

	sk := NewSkynetAppFromHttpHeader(h)
	return sk
}

func (sh *SkynetApp) SetMapping(h *map[string]string) {
	//if sh.AppTraceId != "" {(*h)[AppHeaderTraceId] = sh.AppTraceId}
	/*if sh.AppMethod != "" {
		(*h)[AppHeaderMethod] = sh.AppMethod
	}
	if sh.AppUri != "" {
		(*h)[AppHeaderUri] = sh.AppUri
	}*/
	if sh.AppServer != "" {
		(*h)[AppHeaderServer] = sh.AppServer
	}
	//if sh.AppClient != "" {(*h)[AppHeaderClient] = sh.AppClient}
	if sh.AppTag != "" {
		(*h)[AppHeaderReqTag] = sh.AppTag
	}

	/*
		if sh.AppIp != "" {
			(*h)[AppHeaderIp] = sh.AppIp
		}
	*/

	return
}
