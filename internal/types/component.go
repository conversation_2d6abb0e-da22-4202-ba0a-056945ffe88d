package types

import (
	"strings"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/types"
)

type ComponentType struct {
	types.ComponentType
}

const prefix_unix_sock = "/skynet-runtime/unix/"

func newComponentType(name string) ComponentType {
	return ComponentType{types.ComponentType(name)}
}

const SkynetMesh = "skynet_mesh"

type ComponentKey struct{}

var (
	ComponentHttpForward  = newComponentType("http_forward")
	ComponentGrpcForward  = newComponentType("grpc_forward")
	ComponentTcpHijack    = newComponentType("tcp_hijack")
	ComponentHttpHijack   = newComponentType("http_hijack")
	ComponentGrpcHijack   = newComponentType("grpc_hijack")
	ComponentPub          = newComponentType("pub")
	ComponentSub          = newComponentType("sub")
	ComponentServerCenter = newComponentType("server_center")
	ComponentDiscovery    = newComponentType("discovery")
	ComponentController   = newComponentType("controller")
	ComponentProxy        = newComponentType("proxy")
	ComponentFileBeat     = newComponentType("file_beat")
)

func (c ComponentType) EnvName() string {
	return "SKYNET_MESH_" + strings.ToUpper(c.ToString())
}
