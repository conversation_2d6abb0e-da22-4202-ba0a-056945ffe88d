package types

import (
	"skynet-runtime/internal/logger"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"go.uber.org/zap"
)

const (
	ServerHttp = "http"
	ServerGrpc = "grpc"
	ServerTcp  = "tcp"

	//ForwardServerName = "forward_server"
)

type Component interface {
	Init() error
	Run() error
	Exit()
	Name() ComponentType
	Log() *zap.Logger
}

type DefaultComponent struct {
	name ComponentType
	log  *zap.Logger
}

func NewDefaultComponent(name ComponentType) *DefaultComponent {
	var level = "info"

	if goboot.BootConf().Need() == nil {
		if v, ok := goboot.BootConf().DefaultConfig().Lables["log_level"]; ok {
			level = v.(string)
		}
	}

	lo, err := logger.NewLoggerWithDefault(name.ToString(), level)
	if err != nil {
		panic("create default log faild:" + err.Error())
	}

	return &DefaultComponent{
		name: name,
		log:  lo,
	}
}

func (c *DefaultComponent) Init() error {
	return nil
}

func (c *DefaultComponent) Run() error {
	return nil
}

func (c *DefaultComponent) Exit() {
}

func (c *DefaultComponent) Log() *zap.Logger {
	return c.log
}

func (c *DefaultComponent) Name() ComponentType {
	return c.name
}
