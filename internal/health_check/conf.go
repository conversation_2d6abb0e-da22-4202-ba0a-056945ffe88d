package health_check

import (
	"errors"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

/*
type HealthCheck struct {
	Hcc HealthCheckConf `toml:"health_check"`
}
*/

type HealthCheckConf struct {
	Interval        int `toml:"interval_seoncds" default:"1"`  //检查间隔秒数
	LivenessFailNum int `toml:"liveness_fail_num" default:"3"` //存活态,连续失败3次，认为服务不可用
	//LivenessSuccessNum int    `toml:"liveness_success_num" default:"3"` //存货态,连续成功3次，认为服务可用
	ReadnessFailNum    int    `toml:"readness_fail_num" default:"30"`   //就绪态,总共失败30次，认为服务不可用
	ReadnessSuccessNum int    `toml:"readness_success_num" default:"3"` //就绪态,连续成功3次，认为服务可用
	Timeout            int    `toml:"timeout_seconds" default:"1"`      //超时时间
	Response           string `toml:"response" default:"ok"`            //健康检查返回值
	HealthPath         string `toml:"health_path" default:"/health"`    //健康检查路径
	HealthMethod       string `toml:"health_method" default:"GET"`
}

func (hc HealthCheckConf) validate() error {

	bif := bif.NewOnceIf()
	bif.If(hc.Interval == 0, errors.New("interval is zero"))
	bif.If(hc.LivenessFailNum == 0, errors.New("livess fail num is zero"))
	//bif.If(hc.LivenessSuccessNum == 0, errors.New("livess success num is zero"))
	bif.If(hc.ReadnessFailNum == 0, errors.New("readess fail num is zero"))
	bif.If(hc.ReadnessSuccessNum == 0, errors.New("readness success num is zero"))
	bif.If(hc.Timeout == 0, errors.New("timeout is zero"))

	return bif.Error()
}
