package health_check

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"time"
)

func (w *WatchNode) httpCall() error {
	client := w.getHttpClient()
	if client == nil {
		client = http.DefaultClient
		if w.Unix {
			socketPath := w.Addr
			// 创建自定义 Transport，使用 Unix 域套接字
			tr := &http.Transport{
				Dial: func(network, addr string) (net.Conn, error) {
					return net.Dial("unix", socketPath)
				},
			}

			// 创建 HTTP 客户端，使用自定义 Transport
			client = &http.Client{
				Transport: tr,
			}
		}
		w.setHttpClient(client)
	}

	client = w.getHttpClient()
	url := ""
	if w.Unix {
		url = fmt.Sprintf("http://0.0.0.0%s", w.HealthPath)
	} else {
		url = fmt.Sprintf("http://%s%s", w.Addr, w.HealthPath)
	}

	ctx, cancel := context.WithTimeout(context.TODO(), time.Duration(w.Timeout)*time.Second)
	defer cancel()

	//fmt.Println(w.HealthMethod, url)
	req, err := http.NewRequestWithContext(ctx, w.HealthMethod, url, nil)
	if err != nil {
		return err
	}

	resp, err := client.Do(req)
	if err != nil {
		return err
	}

	defer resp.Body.Close()
	return nil
}
