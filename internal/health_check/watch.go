package health_check

import (
	"net/http"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"
	"sync"
	"time"

	"google.golang.org/grpc"
)

const (
	ModeHttp = "http"
	ModeGrpc = "grpc"
	ModeTcp  = "tcp"
)

type WatchNode struct {
	HealthCheckConf
	//Proto    string
	Addr       string
	Mode       string
	Unix       bool
	rwlock     sync.RWMutex
	statusOk   bool
	calls      map[string]func() error
	grpcConn   *grpc.ClientConn
	httpClient *http.Client
}

func NewWatchNode(addr string, mode string, hc HealthCheckConf) (*WatchNode, error) {
	if err := hc.validate(); err != nil {
		return nil, err
	}

	w := &WatchNode{
		//Proto:           proto,
		Unix:            false,
		Addr:            addr,
		Mode:            mode,
		HealthCheckConf: hc,
		rwlock:          sync.RWMutex{},
		statusOk:        false,
		calls:           map[string]func() error{},
	}

	w.calls = map[string]func() error{
		types.ServerGrpc: w.grpcCall,
		types.ServerHttp: w.httpCall,
		types.ServerTcp:  w.tcpCall,
	}

	return w, nil
}

func (w *WatchNode) setStatus(ok bool) {
	w.rwlock.Lock()
	defer w.rwlock.Unlock()
	w.statusOk = ok
}

func (w *WatchNode) GetStatus() bool {
	w.rwlock.RLock()
	defer w.rwlock.RUnlock()
	return w.statusOk
}

func (w *WatchNode) setGrpcConn(conn *grpc.ClientConn) {
	w.rwlock.Lock()
	defer w.rwlock.Unlock()
	w.grpcConn = conn
}

func (w *WatchNode) getGrpcConn() *grpc.ClientConn {
	w.rwlock.RLock()
	defer w.rwlock.RUnlock()
	return w.grpcConn
}

func (w *WatchNode) setHttpClient(client *http.Client) {
	w.rwlock.Lock()
	defer w.rwlock.Unlock()
	w.httpClient = client
}

func (w *WatchNode) getHttpClient() *http.Client {
	w.rwlock.RLock()
	defer w.rwlock.RUnlock()
	return w.httpClient
}

func (w *WatchNode) Readness() (bok bool) {
	bok = false
	var err error
	defer func() {
		w.setStatus(bok)
		if !bok {
			g_logger.Error(console.Console("health check", "readness fail", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix, err))
		} else {
			g_logger.Info(console.Console("health check", "readness success", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix))
		}
	}()

	successNum := 0
	failNum := 0

	for {
		err = w.healthCall()

		if err != nil {
			g_logger.Error(console.Console("health check", "readness error", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix, err))
			successNum = 0
			failNum += 1
		} else {
			successNum++
		}

		if successNum >= w.ReadnessSuccessNum {
			bok = true
			break
		}

		if failNum >= w.ReadnessFailNum {
			bok = false
			break
		}

		time.Sleep(time.Duration(w.Interval) * time.Second)
	}

	return bok
}

func (w *WatchNode) Watch(fail_chan chan string) {
	w.liveness()
	fail_chan <- w.Addr
}

func (w *WatchNode) liveness() {
	var err error
	defer func() {
		w.setStatus(false)
		g_logger.Error(console.Console("health check", "liveness fail", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix, err))
	}()

	failNum := 0

	for {
		err = w.healthCall()

		if err != nil {
			failNum += 1
			g_logger.Error(console.Fmt("health check", "liveness error", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix, err))
		} else {
			//判断条件是连续失败，因此failNum清零
			failNum = 0
			//g_logger.Info(console.Fmt("health check", "liveness success", w.Mode, w.Addr, w.HealthPath, w.HealthMethod, w.Unix))
		}

		if failNum >= w.LivenessFailNum {
			break
		}

		time.Sleep(time.Duration(w.Interval) * time.Second)
	}

	return
}

func (w *WatchNode) healthCall() error {
	f := w.calls[w.Mode]
	return f()
}
