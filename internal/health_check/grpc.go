package health_check

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
)

func (w *WatchNode) grpcCall() error {
	// 连接 gRPC 服务器
	conn := w.getGrpcConn()
	if conn == nil {
		addr := w.Addr
		if w.Unix {
			addr = "unix://" + w.Addr
		}
		conn, err := grpc.Dial(addr, grpc.WithInsecure())
		if err != nil {
			return fmt.Errorf("health check fail:conn fail: %v", err)
		}
		w.setGrpcConn(conn)
	}

	conn = w.getGrpcConn()

	// 创建 HealthCheck 客户端
	healthClient := grpc_health_v1.NewHealthClient(conn)

	// 创建健康检查请求
	req := &grpc_health_v1.HealthCheckRequest{
		Service: "", // 这里可以是 "" 代表全局检查，也可以是具体的服务名称
	}

	// 发送健康检查请求
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	resp, err := healthClient.Check(ctx, req)
	if err != nil {
		return fmt.Errorf("health check fail: %v", err)
	}

	if resp.Status != grpc_health_v1.HealthCheckResponse_SERVING {
		return errors.New("grpc not serving:" + resp.Status.String())
	}

	return nil
}
