package health_check

import (
	"fmt"
	"net"
	"time"
)

func (w *WatchNode) tcpCall() error {

	if w.Unix {
		conn, err := net.DialTimeout("unix", w.Addr, time.Duration(w.Timeout)*time.Second)
		if err != nil {
			return fmt.Errorf("tcp connect %s fail: %v", w.Addr, err)
		}
		conn.Close()
	} else {
		conn, err := net.DialTimeout("tcp", w.Addr, time.Duration(w.Timeout)*time.Second)
		if err != nil {
			return fmt.Errorf("tcp connect %s fail: %v", w.Addr, err)
		}
		conn.Close()
	}

	return nil
}
