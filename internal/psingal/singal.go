package psingal

import (
	"fmt"
	"os"
	"os/signal"
	"skynet-runtime/internal/console"
	"syscall"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
)

var exitSigs = make(chan os.Signal, 1)

func Singal(fn func()) {
	// 监听指定的信号，例如 SIGINT 和 SIGTERM
	signal.Notify(exitSigs, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

	console.Console("singal", "singal start...")

	// 使用 select 监听信号通道
	for sig := range exitSigs {
		switch sig {
		case syscall.SIGINT, syscall.SIGTERM:
			console.Console("singal", "收到退出信号,开始退出")

			//执行退出函数
			bif.Cond(fn != nil, fn)
			os.Exit(0)
		case syscall.SIGHUP:
			console.Console("singal", "收到 SIGHUP,可以在这里重新加载配置")
		default:
			console.Console("singal", fmt.Sprintf("收到其他信号:%s", sig.String()))
		}
	}
}

func Exit() {
	exitSigs <- syscall.SIGINT
}
