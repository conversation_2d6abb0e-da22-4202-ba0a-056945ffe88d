package logger

import (
	"fmt"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/logger"
	"go.uber.org/zap"
)

type LoggerConf struct {
	Pure      bool   `toml:"pure" default:"false"` //pure 模式，不会添加任何额外的信息，只打印业务日志
	Fpath     string `toml:"file_path"`
	Level     string `toml:"level"`
	MaxSizeMb int    `toml:"max_size_mb" default:"100"`
	MaxBackUp int    `toml:"max_back_up" default:"30"`
	MaxAge    int    `toml:"max_age_day" default:"90"`
	Compress  bool   `toml:"compress" default:"false"`
	Console   bool   `toml:"console" default:"false"`
}

func NewLogger(lc *LoggerConf) (*zap.Logger, error) {

	option := &logger.LogModel{
		Fpath:     lc.Fpath,
		Level:     lc.Level,
		MaxSizeMb: lc.MaxSizeMb,
		MaxBackUp: lc.<PERSON>ack<PERSON>p,
		MaxAge:    lc.<PERSON>,
		Compress:  lc.<PERSON>mp<PERSON>,
		Console:   lc.<PERSON>e,
		Pure:      lc.Pure,
	}
	option.UEnable = true
	option.UName = "component_proxy"
	t_logger, err := logger.NewInstance(option)

	if err != nil {
		return nil, err
	}
	return t_logger, nil
}

const DefaultPreFix = "/skynet-runtime/logs/"

func NewLoggerWithDefault(name, level string) (*zap.Logger, error) {

	option := &logger.LogModel{
		Fpath:     fmt.Sprintf("%s%s.json", DefaultPreFix, name),
		Level:     level,
		MaxSizeMb: 100,
		MaxBackUp: 20,
		MaxAge:    30,
		Compress:  false,
		Console:   false,
		Pure:      false,
	}
	option.UEnable = true
	option.UName = name

	t_logger, err := logger.NewInstance(option)

	if err != nil {
		return nil, err
	}
	return t_logger, nil
}
