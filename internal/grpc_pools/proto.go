package grpc_pools

import "google.golang.org/protobuf/proto"

type Frame struct {
	payload []byte
}

type ProtoCode struct{}

func (pc *ProtoCode) Marshal(v interface{}) ([]byte, error) {

	if d, ok := v.(*Frame); ok {
		return d.payload, nil
	}

	return proto.Marshal(v.(proto.Message))
}

func (pc *ProtoCode) Unmarshal(data []byte, v interface{}) error {

	if d, ok := v.(*Frame); ok {
		d.payload = data
		return nil
	}

	return proto.Unmarshal(data, v.(proto.Message))
}

func (pc *ProtoCode) String() string {
	return "proto"
}
