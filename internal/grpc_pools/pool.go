package grpc_pools

import (
	"context"

	"google.golang.org/grpc"
)

type Factory struct{}

func (f *Factory) Connect(ctx context.Context, addr string) (*grpc.ClientConn, error) {

	conn, err := grpc.DialContext(ctx, addr,
		grpc.WithCodec(&ProtoCode{}),
		grpc.WithInsecure())

	if err != nil {
		return nil, err
	}

	return conn, nil
}

func (f *Factory) Close(addr string, gc *grpc.ClientConn) {
	if gc != nil {
		gc.Close()
	}
}
