package mmetrics

import (
	"fmt"
	"skynet-runtime/internal/types"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/bmetrics"
)

var ForwardKey = fmt.Sprintf("%s_%s", types.SkynetMesh, "forward")
var ForwardLatency *bmetrics.MetricsLatency
var ForwardStatusCounter *bmetrics.MetricsCounter
var ForwardTotalCounter *bmetrics.MetricsCounter
var ForwardConcurrency *bmetrics.MetricsGuage

var HijackKey = fmt.Sprintf("%s_%s", types.SkynetMesh, "hijack")
var HijackTcpConnections *bmetrics.MetricsGuage

var HijackLatency *bmetrics.MetricsLatency
var HijackStatusCounter *bmetrics.MetricsCounter
var HijackTotalCounter *bmetrics.MetricsCounter
var HijackConcurrency *bmetrics.MetricsGuage

func Init() {
	forwardMetrics := bmetrics.NewMetrics()
	forwardMetrics.AddFixLables("srcsrc", goboot.BootConf().DefaultConfig().ServiceName)
	forwardMetrics.AddDynLables("dstsrv", "path", "srvtype")
	ForwardLatency = forwardMetrics.NewLatency(ForwardKey, "requests latency mills")
	ForwardStatusCounter = forwardMetrics.Clone().AddDynLables("status_code").NewCounter(ForwardKey, "requests count")
	ForwardConcurrency = forwardMetrics.NewGuage(ForwardKey+"_status", "requests concurrency")
	ForwardTotalCounter = forwardMetrics.NewCounter(ForwardKey+"_total", "requests count")

	hijackMetrics := bmetrics.NewMetrics()
	hijackMetrics.AddFixLables("srcsrv", fmt.Sprintf("%s_skynetmesh", goboot.BootConf().DefaultConfig().ServiceName))
	hijackMetrics.AddFixLables("dstsrv", goboot.BootConf().DefaultConfig().ServiceName)
	hijackMetrics.AddDynLables("path", "srvtype")
	HijackTcpConnections = hijackMetrics.NewGuage(HijackKey+"_tcp_connections", "tcp connections")

	HijackLatency = hijackMetrics.NewLatency(HijackKey, "requests latency mills")
	HijackStatusCounter = hijackMetrics.Clone().AddDynLables("status_code").NewCounter(HijackKey, "requests count")
	HijackConcurrency = hijackMetrics.NewGuage(HijackKey+"_status", "requests concurrency")
	HijackTotalCounter = hijackMetrics.NewCounter(HijackKey+"_total", "requests count")
}
