package console

import "fmt"

func Console(models ...any) string {
	base := "*********[skynet-runtime]"
	for _, v := range models {
		base += fmt.Sprintf("[%v]", v)
	}

	ss := fmt.Sprintf(base + "*********")
	fmt.Println(ss)
	return ss
}

func Fmt(models ...any) string {
	base := "*********[skynet-runtime]"
	for _, v := range models {
		base += fmt.Sprintf("[%v]", v)
	}

	ss := fmt.Sprintf(base + "*********")
	return ss
}
