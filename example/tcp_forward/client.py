import requests_unixsocket
import requests
import json

def getAddr():
	session = requests_unixsocket.Session()
	socket_path = "/skynet-runtime/unix/http_listen.sock"

	url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/discovery"
	response = session.post(url, timeout=1000,headers = {'Content-Type': 'application/json',
		"skynet-app-server":"runtime_test",
	})

	d = json.loads(response.content)
	host = d["Host"]
	port = d["Port"]
	return host+":"+ str(port)

if __name__ == "__main__":
	addr = getAddr()

	url = f"http://{addr}/api"
	print(url)
	resp = requests.post(
		url=url,
		json={  # 将 data 改为 json
			"name": "test",
		},
		headers={"Content-Type": "application/json"}
	)
	print(resp.text)
	print(resp.status_code)