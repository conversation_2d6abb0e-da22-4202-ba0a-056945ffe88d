[goboot]
enabled=true
service_name="runtime_test"
port=11288
lables.log_level="info"


[proxy]
#targets="7788"
#targets="50052"
#targets="/skynet-runtime/unix/app_grpc_server2.sock"

#tlb配置
[tlb_sdk]
enabled=true
servers="10.103.240.171:30132"

[pub]
values.elk.type="kafka"
values.elk.kafka.brokers = "10.103.240.121:9093,10.103.240.122:9093,10.103.240.123:9093"
values.elk.kafka.topic = "panxu9_test2"

[sub]
values.test.type="kafka"
values.test.kafka.brokers = "10.103.240.121:9093,10.103.240.122:9093,10.103.240.123:9093"
values.test.kafka.topic = "panxu9_test2"
values.test.kafka.group = "sync"
