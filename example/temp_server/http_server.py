from flask import Flask, request, jsonify,make_response
from werkzeug.serving import run_simple
app = Flask(__name__)

@app.route("/health", methods=["GET"])
def home():
    return "Hello, Flask HTTP Server!"

@app.route("/pandora/api", methods=["POST"])
def api():
    data = request.json
    print("api:",request.headers,data)
    response = make_response("Hello with header!")
    return response

@app.route("/pandora/get", methods=["GET"])
def gapi():
    return 'hello get'


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=11230)