import grpc
import sys
import os
import time
import json

# 现在可以使用绝对导入
import ppb_pb2
import ppb_pb2_grpc

from grpc_health.v1 import health, health_pb2, health_pb2_grpc
from concurrent import futures
import requests_unixsocket
import requests
import json
import base64
import threading
import time


class MyService(ppb_pb2_grpc.EchoServiceServicer):
    def Echo(self, request, context):
        context.send_initial_metadata((
            ('x-custom-header', 'value123'),
            ('skynet-app-status-code', '102'),
        ))

        # 获取 gRPC 请求的 metadata
        metadata = context.invocation_metadata()
        # 打印 metadata
        print("Received Metadata:")
        for key, value in metadata:
            print(f"{key}: {value}")
        print("Received request:", request.content,request.id)
        response = ppb_pb2.Message()
        response.content = f"Hello, {request.content}!"
        response.id = 1
  
        return response


def serve():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))  # 创建 gRPC 服务器

       # 注册健康检查服务
    health_servicer = health.HealthServicer()
    health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)

    # 设置服务健康状态
    health_servicer.set("", health_pb2.HealthCheckResponse.SERVING)  # 全局健康状态
    health_servicer.set("MyService", health_pb2.HealthCheckResponse.SERVING)  # 具体服务健康状态


    ppb_pb2_grpc.add_EchoServiceServicer_to_server(MyService(), server)
    server.add_insecure_port(f"0.0.0.0:11230") 
    server.start()  # 启动服务器
    print("gRPC Server started on port ","0.0.0.0:11230")
    server.wait_for_termination()  # 保持服务器运行


if __name__ == '__main__':
    serve()