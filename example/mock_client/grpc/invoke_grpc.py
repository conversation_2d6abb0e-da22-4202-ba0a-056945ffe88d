import grpc
import sys
import os

# 现在可以使用绝对导入
import ppb_pb2
import ppb_pb2_grpc
import json
import time
import threading

channel = grpc.insecure_channel("0.0.0.0:11290")

# 创建一个 gRPC 客户端存根
stub = ppb_pb2_grpc.EchoServiceStub(channel)


def request():
    while True:
        data = {
            "Image": "ubuntu",
            "Cmd": ["echo", "Hello from Unix Socket"],
            "Tty": True
        }
        metadata=[("skynet-app-server","runtime_test"),("skynet-app-grpc-type","unary")]

        print("Request JSON:","send 1")
        response,call = stub.Echo.with_call(ppb_pb2.Message(
            content=json.dumps(data).encode(),
            id=1,
        ),metadata=metadata)

        print("Response:", response)
        print("Received response headers:")
        for key, value in call.initial_metadata():
            print(f"  {key}: {value}")
        time.sleep(1)

if __name__ == '__main__':
    ts = []
    for i in range(1):
        t = threading.Thread(target=request)
        ts.append(t)
    for t in ts:
        t.start()
    
    for t in ts:
        t.join()


