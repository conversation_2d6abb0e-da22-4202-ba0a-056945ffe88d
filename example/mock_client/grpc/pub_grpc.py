import grpc
import sys
import os
import time
import json

# 现在可以使用绝对导入
import ppb_pb2
import ppb_pb2_grpc

def run():
    # 使用 Unix 套接字地址连接到 gRPC 服务端
    socket_path = "unix:///skynet-runtime/unix/grpc_listen.sock"  # 替换为你的 Unix 套接字路径
    #socket_path = "0.0.0.0:5005"
    channel = grpc.insecure_channel(socket_path)

    # 创建一个 gRPC 客户端存根
    stub = ppb_pb2_grpc.SkynetPandoraServiceStub(channel)

    # 获取响应流
    try: 
        while True:
            time.sleep(2)
            action = {
                "action": "service-mesh",
            }

            jstring = json.dumps(action)
            data = {
                "pub_name": "elk",
                "body":  jstring
            }
            # 调用 ServerStream 方法
            metadata=[('uri', '/api/v1/pub')]
            resp = stub.Call(ppb_pb2.RpcRequest(
                data=json.dumps(data).encode()
            ),metadata=metadata)
            print(resp)
    except grpc.RpcError as e:
        print(f"gRPC Error: {e.code()} - {e.details()}")

    print("Stream finished.")

if __name__ == '__main__':
    run()