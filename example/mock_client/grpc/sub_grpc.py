import grpc
import sys
import os
import time
import json

# 现在可以使用绝对导入
import ppb_pb2
import ppb_pb2_grpc


from concurrent import futures
import requests_unixsocket
import requests
import json
import base64
import threading
import time


# 使用 Unix 套接字地址连接到 gRPC 服务端
socket_path = "unix:///skynet-runtime/unix/grpc_listen.sock"  # 替换为你的 Unix 套接字路径
#socket_path = "0.0.0.0:5005"
channel = grpc.insecure_channel(socket_path)

# 创建一个 gRPC 客户端存根
stub = ppb_pb2_grpc.SkynetPandoraServiceStub(channel)

def sub():
    try:
    # 目标 API（示例：创建 Docker 容器）
    #url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/sub"
        data = {
            "sub_name":"test",
            "action":"new",
            "unix":False,
            "proto":"grpc",
            "addr":"0.0.0.0:50052",
            "path":"/api",
        }
        metadata=[('uri', '/api/v1/sub')]
        response = stub.Call(ppb_pb2.RpcRequest(
            data=json.dumps(data).encode()
        ),metadata=metadata)

        print("Response JSON:",response.data.decode())
    except Exception as e:
        print(e)

def cancel():

    try:
        data = {
            "sub_name":"test",
            "action":"cancel",
            "unix":False,
            "proto":"grpc",
            "addr":"0.0.0.0:50052",
            "path":"/api",
        }
        metadata=[('uri', '/api/v1/sub')]
        response = stub.Call(ppb_pb2.RpcRequest(
            data=json.dumps(data).encode()
        ),metadata=metadata)

        print("Response JSON:",response)
    except Exception as e:
        print(e)


def status():
    try:
        data = {
            "sub_name":"test",
            "action":"status",
            "unix":False,
            "proto":"grpc",
            "addr":"0.0.0.0:50052",
            "path":"/api"
        }
        metadata=[('uri', '/api/v1/sub')]
        response = stub.Call(ppb_pb2.RpcRequest(
            data=json.dumps(data).encode()
        ),metadata=metadata)

        print("Response JSON:",response)
    except Exception as e:
        print(e)


if __name__ == '__main__':
    cancel()
    sub()
    while True:
        status()
        time.sleep(1)