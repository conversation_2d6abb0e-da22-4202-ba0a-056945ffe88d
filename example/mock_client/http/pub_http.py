
import requests_unixsocket
import requests
import json
import base64
import time

if __name__ == "__main__":
    session = requests_unixsocket.Session()
    #session = requests.session()

    # Unix Socket 文件路径
    socket_path = "/skynet-runtime/unix/http_listen.sock"

    # 目标 API（示例：创建 Docker 容器）
    url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/pub"
    print(url)
    action = {
        "action": "service-mesh",
    }

    jstring = json.dumps(action)
    data = {
        "pub_name": "elk",
        "body":  jstring
    }

    while True:
        time.sleep(2)
        response = session.post(url, timeout=1000, data=json.dumps(data))

        print("Status Code:", response.status_code)
        print("Response JSON:",response.content)