
import requests_unixsocket
import requests
import json
import base64
import threading
import time


def sub():
    session = requests_unixsocket.Session()
    #session = requests.session()

    # Unix Socket 文件路径
    socket_path = "/skynet-runtime/unix/http_listen.sock"

    # 目标 API（示例：创建 Docker 容器）
    url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/sub"
    data = {
        "sub_name":"test",
        "action":"new",
        "unix":False,
        "proto":"http",
        "addr":"0.0.0.0:7788",
        "path":"/api"
    }
    response = session.post(url, timeout=1000, data=json.dumps(data),headers={
        "skynet-sub-name":"test",
    })

    print("Status Code:", response.status_code)
    print("Response JSON:",response.content)

def cancel():
    session = requests_unixsocket.Session()
    #session = requests.session()

    # Unix Socket 文件路径
    socket_path = "/skynet-runtime/unix/http_listen.sock"

    # 目标 API（示例：创建 Docker 容器）
    url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/sub"

    data = {
        "sub_name":"test",
        "action":"cancel",
        "unix":False,
        "proto":"http",
       "addr":"0.0.0.0:7788",
        "path":"/api"
    }
    response = session.post(url, timeout=1000, data=json.dumps(data),headers={
        "skynet-sub-name":"test",
    })

    print("Status Code:", response.status_code)
    print("Response JSON:",response.content)

def status():
    session = requests_unixsocket.Session()
    #session = requests.session()

    # Unix Socket 文件路径
    socket_path = "/skynet-runtime/unix/http_listen.sock"

    # 目标 API（示例：创建 Docker 容器）
    url = f"http+unix://{socket_path.replace('/', '%2F')}/api/v1/sub"

    data = {
        "sub_name":"test",
        "action":"status",
        "unix":False,
        "proto":"http",
        "addr":"0.0.0.0:7788",
        "path":"/api"
    }
    response = session.post(url, timeout=1000, data=json.dumps(data))

    print("status-----------------",response.status_code,response.content)


if __name__ == "__main__":
    cancel()
    sub()
    while True:
        status()
        time.sleep(1)