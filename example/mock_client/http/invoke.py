import requests

if __name__ == "__main__":
	
	# 发送 POST 请求，创建一个 Ubuntu 容器
	data = {
		"header": {
			"code": 200,
			"success": "true",
			"traceId": "abc123-trace-id",
			"skynet-tlb-service-tag-selector": "canary-v1"
		},
		"payload": {
			"userId": "u123456",
			"action": "login",
			"timestamp": "2025-06-16T10:00:00Z"
		}
	}

	
	response = requests.post("http://0.0.0.0:11289/pandora/api", timeout=1000, json=data,headers={
		"Content-Type": "application/json",
		"skynet-app-server":"runtime_test",
	})

	print("header:", response.headers)
	print("header:", response.status_code)
	print("Response JSON:",response.content)


	response = requests.get("http://0.0.0.0:11289/pandora/get", timeout=1000,headers={
		"Content-Type": "application/json",
		"skynet-app-server":"runtime_test",
	})

	print("header:", response.headers)
	print("header:", response.status_code)
	print("Response JSON:",response.content)