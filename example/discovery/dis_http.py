
import requests_unixsocket
import requests
import json
import base64
import time

if __name__ == "__main__":

    while True:
        time.sleep(2)
        response = requests.post("http://0.0.0.0:11301/api/v1/discovery", timeout=1000,headers = {'Content-Type': 'application/json',
            "skynet-app-server":"runtime_test",
        })

        print("Status Code:", response.status_code)
        print("Response JSON:",response.content)