package filebeat

import (
	"log"
	"testing"

	"github.com/hpcloud/tail"
)

func TestX(t *testing.T) {
	// 指定要监控的文件路径
	filePath := "./logs/test.json"

	// 配置tail
	config := tail.Config{
		Follow:    true, // 跟随文件，监控新增内容
		ReOpen:    true, // 文件被截断后重新打开
		Poll:      true, // 使用轮询模式
		MustExist: false,
	}

	// 使用tail.TailFile创建一个Tail对象
	tailObj, err := tail.TailFile(filePath, config)
	if err != nil {
		log.Fatalf("无法打开文件：%v", err)
	}

	// 循环读取文件内容
	for line := range tailObj.Lines {
		// 每次读取到文件的新内容时，会输出到标准输出
		println(len(line.Text))
	}

	// 程序会一直运行，直到你手动停止它
	// 例如，可以通过发送中断信号（Ctrl+C）来停止程序
}
