package filebeat

import (
	"errors"
	"fmt"
	"log"
	"os"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/types"
	"testing"
	"time"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

type FileBeat struct {
	types.Component
	fileMonitor *Monitor
}

type FileBeatConf struct {
	FilePath string `toml:"file_path" default:"/skynet-runtime/logs/app.json"`
}

func NewFileBeat(fpath string) (*FileBeat, error) {
	fb := &FileBeat{
		Component: types.NewDefaultComponent(types.ComponentFileBeat),
	}
	var err error

	fb.fileMonitor, err = newMonitor(fpath, fb.Component.Log())

	if err != nil {
		return nil, err
	}

	return fb, nil
}

func (fb *FileBeat) Monitor() {
	fb.fileMonitor.Monitor(watchFileFunc())
}

type Monitor struct {
	path    string
	watcher *fsnotify.Watcher
	log     *zap.Logger
}

func newMonitor(path string, log *zap.Logger) (*Monitor, error) {
	m := &Monitor{
		path:    path,
		watcher: nil,
		log:     log,
	}

	var err error
	m.watcher, err = fsnotify.NewWatcher()

	return m, err
}

func (m *Monitor) Monitor(fcall func(m *Monitor) error) {
	for {
		m.watcher.Remove(m.path)
		for {
			err := m.watcher.Add(m.path)
			if err != nil {
				console.Console("filebeat", m.path, "add path fail", err)
				m.watcher.Remove(m.path)
			} else {
				break
			}

			time.Sleep(1 * time.Second)
		}

		console.Console("filebeat", m.path, "add path success")

		err := fcall(m)
		m.log.Error(types.ComponentFileBeat.ToString(), zap.Any("error", err))
	}
}

func watchFileFunc() func(m *Monitor) error {
	return func(m *Monitor) (err error) {
		defer func() {
			console.Console("filebeat", "watch file func exit", err)
		}()
		for {
			select {
			case event, ok := <-m.watcher.Events:
				if !ok {
					return errors.New("filebeat watch chan is closed")
				}
				log.Println("事件:", event.Name, event.Op.String())

				if event.Op == fsnotify.Write {
					//log.Println("文件被修改:", event.Name)
					//fmt
				} else {
					return fmt.Errorf("filebeat watch event %s %s", event.Name, event.String())
				}

			case err, ok := <-m.watcher.Errors:
				if !ok {
					return fmt.Errorf("filebeat watch chan is closed")
				}
				console.Console("filebeat", "watch error", err)
			}
		}
	}
}

func TestF(t *testing.T) {

	os.Mkdir("./logs", os.ModePerm)

	f, err := NewFileBeat("./logs/test.json")
	if err != nil {
		t.Error(err)
	}

	f.Monitor()
}
