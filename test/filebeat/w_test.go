package filebeat

import (
	"testing"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/logger"
	"github.com/bytedance/sonic"
	"go.uber.org/zap"
)

type TraceBase struct {
	SpanId  string         `json:"spanId"`  //用户定义spanId
	TraceId string         `json:"traceId"` //用户定义traceId
	Level   string         `json:"level"`   //用户定义日志等级
	Type    string         `json:"type"`    //用户定义行为，如：http_request_failed
	Tags    map[string]any `json:"tags"`    //用户定义标签

	Timestamp int `json:"time"` //自动填写

	Action      string `json:"action"`      //配置中的servcieName
	Ip          string `json:"ip"`          //本机ip
	SubjectCode string `json:"subjectCode"` //配置中的appid

	Group  string `json:"group"`  //暂时不填
	FromId string `json:"fromId"` //暂时不填
	Port   int    `json:"port"`   //暂时不填
	Pid    int    `json:"pid"`    //暂时不填

	Uuid string `json:"uuid"` //暂时不填
}
type TraceInfo struct {
	TraceBase
	Span string `json:"span"`
	Data string `json:"data"` //用户定义日志内容
}

func newWriter() *zap.Logger {

	option := &logger.LogModel{
		Fpath:     "/skynet_runtime/logs/elk_log.json",
		Level:     "info",
		MaxSizeMb: 1,
		MaxBackUp: 3,
		MaxAge:    30,
		Compress:  false,
		Console:   false,
		Pure:      true,
	}
	option.UEnable = true
	option.UName = "test"

	t_logger, err := logger.NewInstance(option)

	if err != nil {
		panic(err)
	}
	return t_logger
}

func TestW(t *testing.T) {
	lo := newWriter()

	ti := &TraceInfo{
		TraceBase: TraceBase{
			TraceId:   "t3",
			SpanId:    "",
			Level:     "info",
			Timestamp: int(time.Now().UnixMilli()),
			Type:      "testelk",
			Action:    "12345",
			Ip:        "**************",
		},
		Span: "",
		Data: "",
	}

	s, _ := sonic.Marshal(ti)

	for i := 0; i < 10; i++ {
		lo.Info(string(s))
	}

	time.Sleep(5 * time.Second)

}
