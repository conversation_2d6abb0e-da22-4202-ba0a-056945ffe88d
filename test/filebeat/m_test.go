package filebeat

import (
	"testing"

	"github.com/hpcloud/tail"
)

func TestSS(t *testing.T) {
	// 配置tail
	config := tail.Config{
		Follow:    true, // 跟随文件，监控新增内容
		ReOpen:    true, // 文件被截断后重新打开
		Poll:      true, // 使用轮询模式
		MustExist: false,
	}

	// 使用tail.TailFile创建一个Tail对象
	tailObj, _ := tail.TailFile("/skynet_runtime/logs/elk_log.json", config)

	for line := range tailObj.Lines {
		// 处理每一行的日志
		a := line.Text
		println(a)
	}
}
