#export PANDORAGO_GOBOOT="[goboot] enabled=true port=11288 service_name='runtime_test'"
#export PANDORAGO_MESH_CONTROLLER="[controller] enabled=true port=11289 [controller.metrics] url='/metrics'"
#export PANDORAGO_MESH_FILE_BEAT="[[file_beat]] enabled=true file_path='/skynet_runtime/logs/elk_log.json' kafka.brokers='kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092' kafka.topic='lynxiao_flow'"
#export PANDORAGO_TLB_SDK="[tlb_sdk] enabled=true servers='**************:30132'"
go build .
./skynet-runtime server -c ./conf -e