package main

import (
	"encoding/json"
	"skynet-runtime/app"
	"skynet-runtime/cmd"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/psingal"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func main() {
	cmd.Init()

	err := goboot.InitFromConfigAndEnv(cmd.GServerConfig.ConfigPath, cmd.GServerConfig.EnableEnv)
	if err != nil {
		panic(err)
	}

	s, _ := json.Marshal(goboot.BootConf().DefaultConfig())
	console.Console("goboot", string(s))

	//app 初始化，注册路由
	app.AppInit()

	app.AppRun()

	psingal.Singal(func() {
		app.AppExit()
	})
}
