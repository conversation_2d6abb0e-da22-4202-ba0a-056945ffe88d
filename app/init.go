package app

import (
	"skynet-runtime/component/discovery"
	"skynet-runtime/component/file_beat"
	"skynet-runtime/component/proxy"
	"skynet-runtime/component/rpc_server/server_center"
	"skynet-runtime/internal/console"
	"skynet-runtime/internal/mmetrics"
	"skynet-runtime/internal/types"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func assertInit(v types.Component) {
	if err := v.Init(); err != nil {
		panic(console.Console(v.Name(), "Init Error", err.Error()))
	}
	console.Console(v.Name(), "init", "success")
}

func assertRun(v types.Component) {
	if err := v.Run(); err != nil {
		panic(console.Console(v.Name(), "Run Error", err.Error()))
	}
	console.Console(v.Name(), "run", "success")
}

func exitRun(v types.Component) {
	v.Exit()
	console.Console(v.Name(), "run", "success")
}

func AppInit() {

	goboot.BootConf().Must()

	mmetrics.Init()
	//注册component
	RegisterComponent(proxy.Component())
	RegisterComponent(server_center.Component())
	RegisterComponent(file_beat.Component())
	RegisterComponent(discovery.Component())

	for _, v := range g_Components {
		assertInit(v)
	}

}

func AppRun() {
	for _, v := range g_Components {
		assertRun(v)
	}
	return
}

func AppExit() {
	console.Console("app exit")
	for _, v := range g_Components {
		exitRun(v)
	}
	console.Console("app exit done", "sleep 10 seconds")
	time.Sleep(10 * time.Second)
	return
}
